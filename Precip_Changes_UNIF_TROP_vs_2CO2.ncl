; Script to plot precipitation changes with respect to 1CO2 for uniform and tropical simulations

load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_code.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/contributed.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/shea_util.ncl"

begin
  ; Define paths and load data files
  e_path="/home/<USER>/Documents/backup/my_work_draft/writeups/hydrological_cycle"
  e_2CO2 = addfile(e_path+"/Yearly_E_2CO2_01_100new1.nc", "r")
  e_UNIF = addfile("/home/<USER>/Documents/backup/my_work_draft/ITCZ/Yearly_E_37_UNIF_01_100_new1.nc", "r")
  e_TROP = addfile("/home/<USER>/Documents/backup/my_work_draft/ITCZ/Yearly_E2000_2CO2_TROP_01_100_2D_1.nc", "r")
  
  ; Get latitude and longitude
  lat = e_2CO2->lat
  lon = e_2CO2->lon
  
  ; Convert precipitation to mm/day (using years 40-99 for steady state)
  PRECT_2CO2_time_avg = dim_avg_n_Wrap(e_2CO2->PRECT(40:99,:,:), 0) * 8.64e+7
  PRECT_UNIF_time_avg = dim_avg_n_Wrap(e_UNIF->PRECT(40:99,:,:), 0) * 8.64e+7
  PRECT_TROP_time_avg = dim_avg_n_Wrap(e_TROP->PRECT(40:99,:,:), 0) * 8.64e+7
  
  ; Calculate differences with respect to 1CO2
  PRECT_diff_UNIF = PRECT_UNIF_time_avg - PRECT_2CO2_time_avg
  PRECT_diff_TROP = PRECT_TROP_time_avg - PRECT_2CO2_time_avg
a=e_2CO2->PRECT(0,:,:)
printVarSummary(a)
copy_VarCoords(a, PRECT_2CO2_time_avg)
  ; Copy coordinate information
  copy_VarCoords(PRECT_2CO2_time_avg, PRECT_diff_UNIF)
  copy_VarCoords(PRECT_2CO2_time_avg, PRECT_diff_TROP)
  
  ; Calculate global mean precipitation changes using wgt_areaave_Wrap
  rad = 4.0*atan(1.0)/180.0
  wgt = cos(lat * rad)
  
  prect_diff_global_UNIF = ((wgt_areaave_Wrap(PRECT_diff_UNIF, wgt, 1.0, 0))/wgt_areaave_Wrap(PRECT_2CO2_time_avg, wgt, 1.0,0))*100
  prect_diff_global_TROP =((wgt_areaave_Wrap(PRECT_diff_TROP, wgt, 1.0, 0))/wgt_areaave_Wrap(PRECT_2CO2_time_avg, wgt, 1.0,0))*100
 ; print("Global mean precipitation difference (UNIF - 2CO2): " + prect_diff_global_UNIF + " mm/day")
  ;print("Global mean precipitation difference (TROP - 2CO2): " + prect_diff_global_TROP + " mm/day")
  print("Global mean precipitation difference (UNIF - 2CO2): " + prect_diff_global_UNIF + " %")
  print("Global mean precipitation difference (TROP - 2CO2): " + prect_diff_global_TROP + " %")
  ; Create plots
  wks = gsn_open_wks("pdf", "Precip_Changes_UNIF_TROP_vs_2CO2")
  
  ; Common resources for both plots
  res = True
  res@gsnDraw = False
  res@gsnFrame = False
  res@gsnMaximize = True
  res@gsnAddCyclic = True
  
  res@mpFillOn = False
  res@mpOutlineOn = True
  res@mpCenterLonF = 180.0
  
  res@cnFillOn = True
  res@cnLinesOn = False
  res@cnLineLabelsOn = False
  res@cnInfoLabelOn = False
  res@lbLabelBarOn = False
  res@pmLabelBarOrthogonalPosF = -0.01
  
  ; Use a diverging color map for precipitation
  gsn_define_colormap(wks, "BlueWhiteOrangeRed")
;res@cnLevelSelectionMode = "ManualLevels"
 ; res@cnMinLevelValF = -5.0
  ;res@cnMaxLevelValF = 5.0
  res@cnLevelSpacingF = 0.5
  
  ; Create panel plot
  plot = new(2, graphic)
  
  ; UNIF - 1CO2 plot
  ;res@tiMainString = "Precipitation Difference (UNIF - 2CO2)"
  res@gsnLeftString = "UNIF - 2CO2"
  res@gsnRightString ="Mean="+decimalPlaces(prect_diff_global_UNIF,2,True)+ " %"
  plot(0) = gsn_csm_contour_map(wks, PRECT_diff_UNIF, res)
  
  ; TROP - 1CO2 plot
  ;res@tiMainString = "Precipitation Difference (TROP - 2CO2)"
  res@gsnLeftString = "TROP - 2CO2"
  res@gsnRightString = "Mean="+decimalPlaces(prect_diff_global_TROP,2,True)+ " %"
  plot(1) = gsn_csm_contour_map(wks, PRECT_diff_TROP, res)
  
  ; Panel plot resources
  resP = True
  resP@gsnMaximize = True
  resP@gsnPanelLabelBar = True
  resP@lbLabelFontHeightF = 0.012
  ;resP@gsnPanelMainString = "Precipitation Changes with respect to 2CO2"
  
  ; Create the panel plot
  gsn_panel(wks, plot, (/2,1/), resP)
  
  ; Calculate zonal means
  PRECT_2CO2_zonal = dim_avg_n_Wrap(PRECT_2CO2_time_avg, 1)
  PRECT_UNIF_zonal = dim_avg_n_Wrap(PRECT_UNIF_time_avg, 1)
  PRECT_TROP_zonal = dim_avg_n_Wrap(PRECT_TROP_time_avg, 1)
  
  PRECT_diff_UNIF_zonal = PRECT_UNIF_zonal - PRECT_2CO2_zonal
  PRECT_diff_TROP_zonal = PRECT_TROP_zonal - PRECT_2CO2_zonal
  
  ; Make sure zonal means have lat coordinate
  copy_VarCoords(PRECT_2CO2_zonal, PRECT_diff_UNIF_zonal)
  copy_VarCoords(PRECT_2CO2_zonal, PRECT_diff_TROP_zonal)
  
  ; Create data array for zonal plot with proper coordinates
  zonal_data = new((/2, dimsizes(lat)/), float)
  zonal_data(0,:) = PRECT_diff_UNIF_zonal
  zonal_data(1,:) = PRECT_diff_TROP_zonal
  
  ; Add lat coordinate to zonal_data
  zonal_data!1 = "lat"
  zonal_data&lat = lat
  
  ; Create zonal mean plot
  resZ = True
  resZ@gsnDraw = True
  resZ@gsnFrame = True
  resZ@xyLineColors = (/"blue", "red"/)
  resZ@xyLineThicknesses = (/2.5, 2.5/)
  resZ@xyDashPatterns = (/0, 0/)
  resZ@tiMainString = "Zonal Mean Precipitation Change"
  resZ@tiYAxisString = "Precipitation Change (mm/day)"
  resZ@tiXAxisString = "Latitude (°)"
  resZ@trXMinF = -90
  resZ@trXMaxF = 90
  
  resZ@lgLabelFontHeightF = 0.015
  resZ@xyExplicitLegendLabels = (/"UNIF - 2CO2", "TROP - 2CO2"/)
  resZ@pmLegendDisplayMode = "Always"
  resZ@pmLegendSide = "Top"
  resZ@pmLegendParallelPosF = 0.8
  resZ@pmLegendWidthF = 0.15
  resZ@pmLegendHeightF = 0.12
  resZ@lgPerimOn = True
  
  ; Create the zonal mean plot
  plot_zonal = gsn_csm_xy(wks, lat, zonal_data, resZ)
  
end
