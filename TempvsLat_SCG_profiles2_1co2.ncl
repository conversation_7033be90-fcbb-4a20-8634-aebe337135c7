load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_code.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/contributed.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/shea_util.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/wrf/WRFUserARW.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/wrf/WRF_contributed.ncl"

begin
;*************************************************

; e_path="/home/<USER>/Documents/backup/my_work_draft/writeups/hydrological_cycle"
e_path="/home/<USER>/Documents/backup/my_work_draft/writeups/hydrological_cycle"
e_1CO2 = addfile(e_path+"/Yearly_E_CO2_01_100new1.nc", "r")
e_2CO2 = addfile(e_path+"/Yearly_E_2CO2_01_100new1.nc", "r")
e_UNIF_22_5 = addfile(e_path+"/Yearly_E2000_2CO2_UNIF_01_100_2D_1.nc", "r")
e_SOLAR = addfile("/home/<USER>/Documents/Data_solin/29_4_solin/E2000_solin_29_4/Yearly_E2000_solin_29_4_01_48_1.nc", "r")


; Get latitude and longitude
lat = e_1CO2->lat
lon = e_1CO2->lon



; Calculate surface temperature differences with area weighting
; First average over time dimension
TS_2CO2_time_avg = dim_avg_n_Wrap(e_2CO2->TS(40:99,:,:), 0)
TS_1CO2_time_avg = dim_avg_n_Wrap(e_1CO2->TS(40:99,:,:), 0)
TS_UNIF_22_5_time_avg = dim_avg_n_Wrap(e_UNIF_22_5->TS(40:99,:,:), 0)
TS_SOLAR_time_avg = dim_avg_n_Wrap(e_SOLAR->TS(40:47,:,:), 0)

; Compute zonal means (longitude-weighted average at each latitude)
rad = 4.0*atan(1.0)/180.0  ; Convert degrees to radians
clat = cos(lat * rad)   ; latitude weights (not used in this simplified version)

; 2CO2 - 1CO2
delta_TS_2CO2_1CO2 = TS_2CO2_time_avg - TS_1CO2_time_avg
DELTA_T_2CO2_lat = dim_avg_n_Wrap(delta_TS_2CO2_1CO2, 1)  ; Simple average over longitude

; UNIF - 2CO2
delta_TS_UNIF_1CO2 = TS_UNIF_22_5_time_avg - TS_1CO2_time_avg
DELTA_T_UNIF_22_5_lat = dim_avg_n_Wrap(delta_TS_UNIF_1CO2, 1)  ; Simple average over longitude

; SOLAR - 1CO2
delta_TS_SOLAR_1CO2 = TS_SOLAR_time_avg - TS_1CO2_time_avg
DELTA_T_SOLAR_1CO2_lat = dim_avg_n_Wrap(delta_TS_SOLAR_1CO2, 1)  ; Simple average over longitude


; Create a multi-dimensional array for temperature plotting
tempPlotData = new((/3, dimsizes(lat)/), float)
tempPlotData(0,:) = DELTA_T_2CO2_lat  ; 2CO2 - 1CO2
tempPlotData(1,:) = DELTA_T_UNIF_22_5_lat  ; UNIF - 2CO2
tempPlotData(2,:) = DELTA_T_SOLAR_1CO2_lat  ; SOLAR - 1CO2


; Set up plot resources
wks = gsn_open_wks("pdf","Surface_Temp_vs_lat_profiles2_1CO2")

res = True
res@gsnDraw           = True
res@gsnFrame          = True
res@xyLineColors      = (/"red","blue","green"/)
res@xyLineThicknesses = (/2.5, 2.5, 2.5/)
res@xyDashPatterns    = (/0, 0, 0/)         ; all solid lines
res@gsnXYTopLabel     = False
res@tiMainString      = "Surface Temperature Change vs Latitude"
res@tiYAxisString     = "SurfaceTemperature (K)"
res@tiXAxisString     = "Latitude (~F34~0~F~)"
res@trXMinF           = -90
res@trXMaxF           = 90

res@vpHeightF         = 0.6
res@vpWidthF          = 0.75

res@lgLabelFontHeightF = 0.015
res@xyExplicitLegendLabels = (/"2xCO~B~2 - 1xCO~B~2", "SAI - 1xCO~B~2", "SOLAR - 1xCO~B~2"/)
res@pmLegendDisplayMode = "Always"
res@pmLegendSide = "Top"     ; or "Bottom", "Top", etc.
res@pmLegendParallelPosF = 0.8 ; fine-tune positioning
res@pmLegendWidthF         =  0.25                       ;-- define legend width
res@pmLegendHeightF        =  0.15
res@pmLegendOrthogonalPosF = -0.40
res@lgPerimOn = True           ; legend box border

; Create the plot
plot = gsn_csm_xy(wks, lat, tempPlotData, res)
print(tempPlotData(0,:))
exit()

end



