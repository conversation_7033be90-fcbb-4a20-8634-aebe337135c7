; Script to create spatial maps of temperature and precipitation differences
; between tropical case and 1CO2 case

load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_code.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/contributed.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/shea_util.ncl"

begin
  ; Define paths and load data files
  e_path="/home/<USER>/Documents/backup/my_work_draft/writeups/hydrological_cycle"
  e_1CO2 = addfile(e_path+"/Yearly_E_CO2_01_100new1.nc", "r")
  e_TROP = addfile("/home/<USER>/Documents/backup/my_work_draft/ITCZ/Yearly_E2000_2CO2_TROP_01_100_2D_1.nc", "r")
  
  ; Get latitude and longitude
  lat = e_1CO2->lat
  lon = e_1CO2->lon
  
  ; Convert precipitation to mm/day
  PRECT_1CO2_time_avg = dim_avg_n_Wrap(e_1CO2->PRECT(40:99,:,:), 0) *8.64e+7
  PRECT_TROP_time_avg = dim_avg_n_Wrap(e_TROP->PRECT(40:99,:,:), 0) *8.64e+7
  
  ; Calculate differences (TROP - 1CO2)
  PRECT_diff = PRECT_TROP_time_avg - PRECT_1CO2_time_avg
  
  ; Copy coordinate information
  copy_VarCoords(PRECT_1CO2_time_avg, PRECT_diff)
  
  jlat  = dimsizes( lat )
  rad    = 4.0*atan(1.0)/180.0
  re     = 6371220.0
  rr     = re*rad
  dlon   = abs(lon(2)-lon(1))*rr
  dx    = dlon*cos(lat*rad)
  dy     = new ( jlat, typeof(dx))
  dy(0)  = abs(lat(2)-lat(1))*rr
  dy(1:jlat-2)  = abs(lat(2:jlat-1)-lat(0:jlat-3))*rr*0.5   
  dy(jlat-1)    = abs(lat(jlat-1)-lat(jlat-2))*rr
  area   = dx*dy
  clat   = cos(lat*rad)
;****************************************************

 
Global_mean_percent=(wgt_areaave_Wrap(PRECT_diff,area,1.0,0)/wgt_areaave_Wrap(PRECT_1CO2_time_avg,area,1.0,0))*100.
copy_VarCoords(PRECT_1CO2_time_avg,Global_mean_percent)

Label_Global_mean_percent=("Mean="+decimalPlaces(Global_mean_percent,2,True)+"%")
print(Global_mean_percent)

  printVarSummary(PRECT_diff)
  print(PRECT_diff(:,1))
  ; Create plots
  wks = gsn_open_wks("pdf", "TROP_vs_1CO2_spatial_maps_PT")
  
  ; Common resources for both plots
  gsn_define_colormap(wks,"MPL_RdBu") ;

 
  ; Common resources for both plots
  res = True
  res@gsnDraw = False
  res@gsnFrame = False
  res@gsnMaximize = True
  res@gsnAddCyclic = True
  
  res@mpFillOn = False
  res@mpOutlineOn = True
  res@mpCenterLonF = 180.0
  
  res@cnFillOn = True
  res@cnLinesOn = False
  res@cnLineLabelsOn = False
  res@cnInfoLabelOn = False
  res@lbLabelBarOn = True
  res@pmLabelBarOrthogonalPosF = -0.01
  
  ; Temperature plot resources
  resT = res
  resT@tiMainString = "Precipitation (TROP - 1CO2)"
  resT@gsnLeftString = "TROP - 1CO2"
  resT@gsnRightString = "mm/day"
  resT@tiMainFontHeightF = 0.015
resT@tiMainFontHeightF = 0.015
resT@tiMainFontHeightF = 0.015
  ; Use a diverging color map for temperature
  gsn_define_colormap(wks, "MPL_RdBu")
  resT@cnLevelSelectionMode         = "ExplicitLevels"
  resT@cnLevels            = (/-3,-2.5,-2,-1.5,-1,-0.5,0,0.25,0.5,0.75,1,1.25,1.5/)
  ;resT@cnMinLevelValF = -2
  ;resT@cnMaxLevelValF = 2
 ; resT@cnLevelSpacingF = 0.15
  resT@gsnPaperOrientation = "portrait"
  resT@tmYRBorderOn = False
  resT@tmYLBorderOn = False
  resT@tmXBBorderOn = False
  resT@tmXTBorderOn = False
  resT@tmYLOn               = False     ; Turn off top tickmarks
  resT@tmYROn               = False 
  resT@tmXBOn               = False     ; Turn off top tickmarks
  resT@tmXTOn               = False 
  resT@gsnRightString = Label_Global_mean_percent
  resT@lbTitleString             = "mm ~NN~ day~S~-1" ;"kJ/cm^2"
  resT@lbTitleFontHeightF        = 0.01
  resT@lbTitleFont               = "times-roman"
  resT@lbTitlePosition           = "Bottom"
  ; Create the plots
  plotT = gsn_csm_contour_map(wks, PRECT_diff, resT)
  
  ; Draw the plots
  draw(plotT)
  frame(wks)
  
  
end