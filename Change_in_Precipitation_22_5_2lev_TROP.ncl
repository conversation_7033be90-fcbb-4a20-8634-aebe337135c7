



load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_code.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/contributed.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/shea_util.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/wrf/WRFUserARW.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/wrf/WRF_contributed.ncl"

begin
;*************************************************
e_path1="/home/<USER>/Documents/newdata_pgms"
e_1CO2=addfile(e_path1+"/Yearly_E_CO2_01_100new.nc","r")
e_2CO2 = addfile(e_path1+"/Yearly_E2000_2CO2_01_100new1.nc", "r")
e_UNIF = addfile("/home/<USER>/Documents/Data/Yearmean_data/SOM/22_5/Yearly_E2000_2CO2_UNIF_01_100_2D_1.nc", "r")

e_EQUI = addfile("/home/<USER>/Documents/Data/Yearmean_data/SOM/22_5/Yearly_E2000_2CO2_TROP_01_100_2D_1.nc", "r")
e_POLA = addfile("/home/<USER>/Documents/Data/Yearmean_data/SOM/22_5/Yearly_E2000_2CO2_POLAR_01_100_2D_1.nc", "r")
e_ARCT = addfile("/home/<USER>/Documents/Data/Yearmean_data/SOM/22_5/Yearly_E2000_2CO2_ARCTIC_01_100_2D_1.nc", "r")
e_ANTA = addfile("/home/<USER>/Documents/Data/Yearmean_data/SOM/22_5/Yearly_E2000_2CO2_ANTARC_01_100_2D_1.nc", "r")


 

PRECC_e_1CO2=e_1CO2->PRECC(40:99,:,:)
PRECC_e_2CO2=e_2CO2->PRECC(40:99,:,:)
PRECC_e_UNIF=e_UNIF->PRECC(40:99,:,:)
PRECC_e_EQUI=e_EQUI->PRECC(40:99,:,:)
PRECC_e_POLA=e_POLA->PRECC(40:99,:,:)
PRECC_e_ARCT=e_ARCT->PRECC(40:99,:,:)
PRECC_e_ANTA=e_ANTA->PRECC(40:99,:,:)
 
PRECL_e_1CO2=e_1CO2->PRECL(40:99,:,:)
PRECL_e_2CO2=e_2CO2->PRECL(40:99,:,:)
PRECL_e_UNIF=e_UNIF->PRECL(40:99,:,:)
PRECL_e_EQUI=e_EQUI->PRECL(40:99,:,:)
PRECL_e_POLA=e_POLA->PRECL(40:99,:,:)
PRECL_e_ARCT=e_ARCT->PRECL(40:99,:,:)
PRECL_e_ANTA=e_ANTA->PRECL(40:99,:,:)

P_e_1CO2=(PRECC_e_1CO2*8.64e+7)+(PRECL_e_1CO2*8.64e+7)
P_e_2CO2=(PRECC_e_2CO2*8.64e+7)+(PRECL_e_2CO2*8.64e+7) 
P_e_UNIF=(PRECC_e_UNIF*8.64e+7)+(PRECL_e_UNIF*8.64e+7)
P_e_EQUI=(PRECC_e_EQUI*8.64e+7)+(PRECL_e_EQUI*8.64e+7) 
P_e_POLA=(PRECC_e_POLA*8.64e+7)+(PRECL_e_POLA*8.64e+7)
P_e_ARCT=(PRECC_e_ARCT*8.64e+7)+(PRECL_e_ARCT*8.64e+7) 
P_e_ANTA=(PRECC_e_ANTA*8.64e+7)+(PRECL_e_ANTA*8.64e+7)
;---------------------------------------------------------
dummy=e_1CO2->PRECC(0:6,:,:)
dummy@_FillValue =-999.999


P_mean=new((/7,96,144/),typeof(dummy),dummy@_FillValue)
P_mean(0,:,:)=dim_avg_n_Wrap(P_e_2CO2,0)
P_mean(1,:,:)=dim_avg_n_Wrap(P_e_1CO2,0)
P_mean(2,:,:)=dim_avg_n_Wrap(P_e_UNIF,0)
P_mean(3,:,:)=dim_avg_n_Wrap(P_e_EQUI,0)
P_mean(4,:,:)=dim_avg_n_Wrap(P_e_POLA,0)
P_mean(5,:,:)=dim_avg_n_Wrap(P_e_ARCT,0)
P_mean(6,:,:)=dim_avg_n_Wrap(P_e_ANTA,0)
copy_VarCoords(dummy,P_mean)
printVarSummary(P_mean)

P_variance=new((/7,96,144/),typeof(dummy),dummy@_FillValue)
P_variance(0,:,:)=dim_variance_n_Wrap(P_e_2CO2,0)
P_variance(1,:,:)=dim_variance_n_Wrap(P_e_1CO2,0)
P_variance(2,:,:)=dim_variance_n_Wrap(P_e_UNIF,0)
P_variance(3,:,:)=dim_variance_n_Wrap(P_e_EQUI,0)
P_variance(4,:,:)=dim_variance_n_Wrap(P_e_POLA,0)
P_variance(5,:,:)=dim_variance_n_Wrap(P_e_ARCT,0)
P_variance(6,:,:)=dim_variance_n_Wrap(P_e_ANTA,0)
copy_VarCoords(dummy,P_variance)

 

;---------------------------------------------------------------
dummy2=e_1CO2->PRECC(0:5,:,:)
dummy2@_FillValue =-999.999

Change_P=new((/6,96,144/),typeof(dummy2),dummy2@_FillValue)
Change_P(0,:,:)=P_mean(1,:,:)-P_mean(0,:,:)
Change_P(1,:,:)=P_mean(2,:,:)-P_mean(0,:,:)
Change_P(2,:,:)=P_mean(3,:,:)-P_mean(0,:,:)
Change_P(3,:,:)=P_mean(4,:,:)-P_mean(0,:,:)
Change_P(4,:,:)=P_mean(5,:,:)-P_mean(0,:,:)
Change_P(5,:,:)=P_mean(6,:,:)-P_mean(0,:,:)
copy_VarCoords(dummy2,Change_P)


prob_P=new((/6,96,144/),typeof(dummy),dummy@_FillValue)

prob_P(0,:,:)=ttest(P_mean(0,:,:),P_variance(0,:,:),60,P_mean(1,:,:),P_variance(1,:,:),60,False,False)
prob_P(1,:,:)=ttest(P_mean(0,:,:),P_variance(0,:,:),60,P_mean(2,:,:),P_variance(2,:,:),60,False,False)
prob_P(2,:,:)=ttest(P_mean(0,:,:),P_variance(0,:,:),60,P_mean(3,:,:),P_variance(3,:,:),60,False,False)
prob_P(3,:,:)=ttest(P_mean(0,:,:),P_variance(0,:,:),60,P_mean(4,:,:),P_variance(4,:,:),60,False,False)
prob_P(4,:,:)=ttest(P_mean(0,:,:),P_variance(0,:,:),60,P_mean(5,:,:),P_variance(5,:,:),60,False,False)
prob_P(5,:,:)=ttest(P_mean(0,:,:),P_variance(0,:,:),60,P_mean(6,:,:),P_variance(6,:,:),60,False,False)
copy_VarCoords(dummy2,prob_P)

alpha=(1-prob_P)*100
copy_VarCoords(dummy2,alpha)

Change_sig=mask(Change_P,(alpha.ge.95),True) ;90% 
copy_VarCoords(dummy2,Change_sig)
;---------------------------------------------------------
;******************************************                 Areal average
lat=e_1CO2->lat
lon=e_1CO2->lon


  jlat  = dimsizes( lat )
  rad    = 4.0*atan(1.0)/180.0
  re     = 6371220.0
  rr     = re*rad
  dlon   = abs(lon(2)-lon(1))*rr
  dx    = dlon*cos(lat*rad)
  dy     = new ( jlat, typeof(dx))
  dy(0)  = abs(lat(2)-lat(1))*rr
  dy(1:jlat-2)  = abs(lat(2:jlat-1)-lat(0:jlat-3))*rr*0.5   
  dy(jlat-1)    = abs(lat(jlat-1)-lat(jlat-2))*rr
  area   = dx*dy
  clat   = cos(lat*rad)
;****************************************************

 
Global_mean_percent=(wgt_areaave_Wrap(Change_P,area,1.0,0)/wgt_areaave_Wrap(P_mean(0,:,:),area,1.0,0))*100.
copy_VarCoords(dummy2,Global_mean_percent)

Label_Global_mean_percent=("Mean="+decimalPlaces(Global_mean_percent,2,True)+"%")
print(Global_mean_percent)

print(wgt_areaave_Wrap(P_mean(1,:,:),area,1.0,0))

;------------------------------------------------------------------------------------------------------------------------------- 
 
   Change_sig&lat@units="degrees_north"
   Change_sig&lon@units="degrees_east"

;------------------------------------------------ 
;  wks  = gsn_open_wks("eps","plot_precipitation_change_22_5_2lev")      ; send graphics to PNG file
;  wks  = gsn_open_wks("pdf","plot_precipitation_change_22_5_2lev")      ; send graphics to PNG file
 wks  = gsn_open_wks("png","plot_precipitation_change_22_5_2lev")      ; send graphics to PNG file

;___________________________________________________________________________________________________________________
;----------------------------------------------------------------------
; Set list of map projections. Not including "PseudoMollweide" here.
;----------------------------------------------------------------------
  projections = (/"Orthographic","Stereographic","PolarStereographic",\
                  "LambertEqualArea","Gnomonic","AzimuthalEquidistant",\
                  "Satellite","Mercator","CylindricalEquidistant",\
                  "LambertConformal","MaskedLambertConformal",\
                  "Robinson","CylindricalEqualArea","RotatedMercator",\
                  "Aitoff","Hammer","Mollweide","WinkelTripel"/)
  nproj = dimsizes(projections)
 
;----------------------------------------------------------------------
  gsn_define_colormap(wks,"MPL_RdBu") ;

  cnres                             = True
  cnres@gsnMaximize                 = False
  ;cnres@gsnMaximize = False
  ;cnres@cnFillDrawOrder             = "PreDraw"       ; draw contours before continents
  cnres@gsnDraw                     = False
  cnres@gsnFrame                    = False
  cnres@cnLinesOn                   = False
  cnres@cnLineThicknessF            = 0.5
  cnres@cnLineLabelsOn              = False
  cnres@cnFillOn                    = True

  ;cnres@mpFillOn                    = False
  ;cnres@mpGeophysicalLineColor      = "black"
  ;cnres@mpGeophysicalLineThicknessF = 1
  cnres@mpLandFillColor              = "white" ;"cornsilk" ;"black"      ;darkolivegreen ;goldenrod4
  ;cnres@mpLandFillPattern           = 4
  cnres@gsnAddCyclic                 = True

  ;cnres@mpProjection                  = "Robinson"
  ;cnres@mpPerimOn                     = False         ; turn off map perimeter

  cnres@mpCenterLonF                  = 180
  ;cnres@mpLimitMode                  = "LatLon"
  ;cnres@mpMinLatF                    = -60
  ;cnres@mpMaxLatF                    = 60
  ;cnres@mpMinLonF                    = 0
  ;cnres@mpMaxLonF                    = 360
  cnres@cnLevelSelectionMode         = "ManualLevels"
  cnres@cnMinLevelValF               = -2.0
  cnres@cnMaxLevelValF               = 2.0
  cnres@cnLevelSpacingF              = 0.25
  cnres@lbLabelStride                = 4
  cnres@gsnRightStringFontHeightF    = -0.033
  cnres@gsnLeftStringFontHeightF     = -0.033

  cnres@gsnRightString               = ""
  cnres@gsnLeftString                = ""
  cnres@tiMainString                 = "" ;
  cnres@tiMainFont                   = "times-roman"
  cnres@lbLabelBarOn                 = False            ; turn off individual cb's
  cnres@lbBoxEndCapStyle             = "TriangleBothEnds"
  ;cnres@lbBoxEndCapStyle            = "TriangleHighEnd"
  cnres@pmLabelBarWidthF             = 0.04
  cnres@pmLabelBaSSTeightF           = 0.23
  cnres@lbOrientation                = "Vertical"     ; vertical label bar

  cnres@pmTickMarkDisplayMode = "Always"            ; turn on built-in tickmarks
  cnres@lbTitleOn             = True
  cnres@lbLabelStride         = 2
  cnres@lbTitleString         = "P (mm/day)" ;"kJ/cm^2"
  cnres@lbTitleFontHeightF    = 0.022
  cnres@lbTitleFont           = "times-roman"

  cnres@tmXBLabelFontHeightF    = 0.022    ; Make these labels smaller.
  cnres@tmYLLabelFontHeightF    = 0.022    ; Make these labels smaller.


  cnres@tiYAxisFont 	     = "times-roman"
  cnres@tiXAxisFont          = "times-roman"


  
   cnres@tmXBMajorLengthF     = 0.01
   cnres@tmYLMajorLengthF     = 0.01


  cnres@tmYLLabelFont                  = "times-roman"
  cnres@tmXBLabelFont                  = "times-roman"
  cnres@tmXBLabelsOn                   = False

  cnres@tmYRBorderOn = False
  cnres@tmYLBorderOn = False
  cnres@tmXBBorderOn = False
  cnres@tmXTBorderOn = False

  cnres@tmYRLabelsOn = True
  cnres@tmXBMajorLengthF=0.03
  cnres@tmYRMajorLengthF=0.03
  cnres@tmXBOn               = False     ; Turn off top tickmarks
  cnres@tmXTOn               = False     ; Turn off top tickmarks
  ;cnres@tmYLOn               = False     ; Turn off left tickmarks
  ;cnres@tmYROn               = True      ; Turn off bottom tickmarks

  cnres@tmXBMajorOutwardLengthF =-0.02
  cnres@tmXBLabelStride =2
  cnres@tmYLLabelStride =2


  ;cnres@gsnLeftString        = ""
  ;cnres@gsnLeftStringFontHeightF = 0.019


  ;cnres@gsnZonalMean                = True         ; add zonal plot
  ;cnres@gsnZonalMeanXMinF           = -1.          ; set minimum X-axis value for zonal mean plot  
  ;cnres@gsnZonalMeanXMaxF           = 1.           ; set maximum X-axis value for zonal mean plot  
  ;cnres@gsnZonalMeanYRefLine        = 0.0          ; set reference line X-axis value



  ;cnres@vpWidthF              =  1.0
  ;cnres@vpHeightF             =  1.0
  ;cnres@vpXF                  =  0.05
  ;cnres@vpYF                  =  0.95

  dlon = 30
  dlat = 30

  cnres@mpProjection          = "Robinson"
  cnres@mpPerimOn             =  True
  cnres@mpGridAndLimbOn       =  True
  cnres@mpGridLatSpacingF     =  dlat
  cnres@mpGridLonSpacingF     =  dlon
  cnres@mpGridLineColor       = "gray"


  ;cnres@lbBoxMinorExtentF     =  0.15                   ;-- decrease height of labelbar
 
;------------------------------------------------ 
zres              = True
zres@gsnDraw                     = False
zres@gsnFrame                    = False

;zres@gsnMaximize = False

zres@tmXBMode     = "Explicit"                ; Define own tick mark labels.
zres@tmXBValues   = (/-1.5,0,1.5/)
zres@tmXBLabels   = zres@tmXBValues + ""   ;
zres@tmXBLabelFontColor   = "dodgerblue1" 


zres@trXMinF                 = -2.      ; Could also use gsnZonalMeanXMinF
zres@trXMaxF                 = 2.      ; Could also use gsnZonalMeanXMaxF

zres@xyLineThicknessF        = 3.5
zres@xyLineColor             =  "dodgerblue1" ;mediumslateblue dodgerblue1
zres@gsnXRefLine             = 0
zres@gsnZonalMeanYRefLine    = 10
zres@gsnYRefLineThicknessesF = 0.05
zres@gsnYRefLineDashPatterns = 1
zres@gsnYRefLineColor        = "grey53"


zres@tiMainString              = "" 
zres@gsnRightString            = ""
zres@gsnRightStringFont        = "times-roman"
zres@gsnRightStringFontHeightF = 0.022

 


zres@tmXTOn                   = False      ; Turn off top tickmarks
zres@tmYLOn                   = False     ; Turn off bottom tickmarks
zres@tmYROn                   = False      ; Turn off bottom tickmarks
;zres@tmXBLabelAngleF          =45
zres@tmYRLabelsOn = False


zres@tmXBLabelFontThicknessF = 1.5
zres@tmXBLabelFontHeightF    = 0.030    ; Make these labels smaller.
;zres@tmYRLabelFontHeightF    = 0.053    ; Make these labels smaller.
zres@tmXBLabelFont           = "times-roman"
zres@tmYRLabelFont           = "times-roman"

;zres@tmYRMinorOn = False

zres@tiYAxisFont = "times-roman"
zres@tiYAxisString = "Latitude"
zres@tiYAxisSide = "Right"

;zres@tiYAxisFontHeightF = 0.033

   ;zres@tmXBMajorLengthF     = 0.02
   ;zres@tmYLMajorLengthF     = 0.008
   ;zres@tmYRMinorLengthF     = 0.00

;zres@tmYRMajorOutwardLengthF =-0.01
;zres@tmYRLabelStride =2



 





zres@vpWidthF               =  0.2
;zres@vpHeightF             =  0.46
;zres@vpXF                  =  0.05
;zres@vpYF                  =  0.95

zres2 = zres
;zres2@gsnXYFillColors = "LightBlue"
;zres2@xyLineColor     = -1
zres2@xyLineColor             =  "dodgerblue1" ;
zres2@gsnYRefLineThicknessesF = 0.1 
; Create a plot with the area between both curves filled in pink.
  delete(zres2@xyLineColors)
  zres2@gsnXYFillColors = "deepskyblue"
  zres2@gsFillOpacityF  = 0.1
  zres2@xyLineColor     = -1                           ; We don't want the line, so make it transparent.


zres3=zres2
zres3@gsnXYFillColors = "grey57"
zres3@xyLineColor     = -1
 
;--------------------------

zonalmean_1CO2=dim_avg_n_Wrap(P_e_1CO2(:,:,:), (/2/))  
TStd_dev_1CO2     = dim_stddev_n_Wrap(zonalmean_1CO2(:,:), (/0/)) 

Xmean_change      = dim_avg_n_Wrap(Change_P(1,:,:), (/1/) )    



 
R_STD_T = 2*(TStd_dev_1CO2)
L_STD_T = -1*(2*TStd_dev_1CO2)
copy_VarCoords(Xmean_change,R_STD_T)
copy_VarCoords(Xmean_change,L_STD_T)


print(R_STD_T)
print(L_STD_T)
 
nlat  = dimsizes(lat)  
SET1      = new ((/2,nlat/), float)
SET1(0,:)=R_STD_T(:)
SET1(1,:)=L_STD_T(:) 
 

;--------------------------
 

xStd_dev     = dim_stddev_n_Wrap(Change_P(:,:,:), (/2/) )  
xmean_change = dim_avg_n_Wrap(Change_P(:,:,:), (/2/) )  

R_STD = xmean_change+xStd_dev
L_STD = xmean_change-xStd_dev
copy_VarCoords(xmean_change,R_STD)
copy_VarCoords(xmean_change,L_STD)



nlat  = dimsizes(lat)  
SET      = new ((/12,nlat/), float)
SET(0,:)=R_STD(0,:)
SET(1,:)=L_STD(0,:)
SET(2,:)=R_STD(1,:)
SET(3,:)=L_STD(1,:)
SET(4,:)=R_STD(2,:)
SET(5,:)=L_STD(2,:)
SET(6,:)=R_STD(3,:)
SET(7,:)=L_STD(3,:)
SET(8,:)=R_STD(4,:)
SET(9,:)=L_STD(4,:)
SET(10,:)=R_STD(5,:)
SET(11,:)=L_STD(5,:)
 
 
print(R_STD&lat)
print(SET(0:1,:))

 

       

 
;************************************************ 
 
;------------------------------------------------ 
Cases_Label = (/"(a) 1xCO~B1~2 ~NN~"," (b) Uniform","(c) Tropic","(d) Polar","(e) Arctic","(f) Antarctic"/)
               
  plot = new(6,graphic)
  cnres@gsnLeftString  = Cases_Label(0)
  cnres@gsnRightString = Label_Global_mean_percent(0)
  plot(0)              = gsn_csm_contour_map(wks,Change_P(0,:,:),cnres)
  zonal                = gsn_csm_attach_zonal_means(wks,plot(0),Change_P(0,:,:),zres)
  ;STD_plot  = gsn_csm_xy(wks,SET(0:1,:), R_STD&lat,zres2)  ; Create another filled XY plot.
  ;overlay(zonal,STD_plot)
  STD_plot1  = gsn_csm_xy(wks,SET1(0:1,:), R_STD&lat,zres3)  ; Create another filled XY plot.
  overlay(zonal,STD_plot1)

  ;zonal_plot = gsn_csm_zonal_means(wks,Change_P(0,:,:),zres2)
  ;zonal_plot_R  = gsn_csm_xy (wks,R_STD(0,:),lat,zres2)   ; create plot. 
  ;zonal_plot_L  = gsn_csm_xy (wks,L_STD(0,:),lat,zres2)   ; create plot. 
  ;overlay(zonal_mean,zonal_plot_R)
  ;overlay(zonal,zonal_plot_L)
  ;zonal          = gsn_csm_attach_zonal_means(wks,plot(0),Change_P(0,:,:),zres)
 

  
  ;zonal_mean           = gsn_csm_attach_zonal_means(wks,plot(0),Change_P(0,:,:),zres2)
 
  cnres@gsnLeftString  = Cases_Label(1)
  cnres@gsnRightString = Label_Global_mean_percent(1)
  plot(1)              = gsn_csm_contour_map(wks,Change_P(1,:,:),cnres)
  zonal                = gsn_csm_attach_zonal_means(wks,plot(1),Change_P(1,:,:),zres)
  ;STD_plot  = gsn_csm_xy(wks,SET(2:3,:), R_STD&lat,zres2)  ; Create another filled XY plot.
  ;overlay(zonal,STD_plot)
  STD_plot1  = gsn_csm_xy(wks,SET1(0:1,:), R_STD&lat,zres3)  ; Create another filled XY plot.
  overlay(zonal,STD_plot1)

  cnres@gsnLeftString  = Cases_Label(2)
  cnres@gsnRightString = Label_Global_mean_percent(2)  
  plot(2)              = gsn_csm_contour_map(wks,Change_P(2,:,:),cnres)
  zonal                = gsn_csm_attach_zonal_means(wks,plot(2),Change_P(2,:,:),zres)
  ;STD_plot  = gsn_csm_xy(wks,SET(4:5,:), R_STD&lat,zres2)  ; Create another filled XY plot.
  ;overlay(zonal,STD_plot)
  STD_plot1  = gsn_csm_xy(wks,SET1(0:1,:), R_STD&lat,zres3)  ; Create another filled XY plot.
  overlay(zonal,STD_plot1)
 
  cnres@gsnLeftString  = Cases_Label(3) 
  cnres@gsnRightString = Label_Global_mean_percent(3)
  plot(3)              = gsn_csm_contour_map(wks,Change_P(3,:,:),cnres)
  zonal                = gsn_csm_attach_zonal_means(wks,plot(3),Change_P(3,:,:),zres)
  ;STD_plot  = gsn_csm_xy(wks,SET(6:7,:), R_STD&lat,zres2)  ; Create another filled XY plot.
  ;overlay(zonal,STD_plot)
  STD_plot1  = gsn_csm_xy(wks,SET1(0:1,:), R_STD&lat,zres3)  ; Create another filled XY plot.
  overlay(zonal,STD_plot1)


  cnres@gsnLeftString  = Cases_Label(4)
  cnres@gsnRightString = Label_Global_mean_percent(4)
  plot(4)              = gsn_csm_contour_map(wks,Change_P(4,:,:),cnres)
  zonal                = gsn_csm_attach_zonal_means(wks,plot(4),Change_P(4,:,:),zres)
  ;STD_plot  = gsn_csm_xy(wks,SET(8:9,:), R_STD&lat,zres2)  ; Create another filled XY plot.
  ;overlay(zonal,STD_plot)
  STD_plot1  = gsn_csm_xy(wks,SET1(0:1,:), R_STD&lat,zres3)  ; Create another filled XY plot.
  overlay(zonal,STD_plot1)


  cnres@gsnLeftString  = Cases_Label(5)
  cnres@gsnRightString = Label_Global_mean_percent(5)
  plot(5)              = gsn_csm_contour_map(wks,Change_P(5,:,:),cnres)
  zonal                = gsn_csm_attach_zonal_means(wks,plot(5),Change_P(5,:,:),zres)
  ;STD_plot  = gsn_csm_xy(wks,SET(10:11,:), R_STD&lat,zres2)  ; Create another filled XY plot.
  ;overlay(zonal,STD_plot)
  STD_plot1  = gsn_csm_xy(wks,SET1(0:1,:), R_STD&lat,zres3)  ; Create another filled XY plot.
  overlay(zonal,STD_plot1)

  

;------------------------------
; plot statistical significance
;------------------------------

  sgres                      = True		; significance
  sgres@gsnDraw              = False		; draw plot
  sgres@gsnFrame             = False		; advance frome
  sgres@cnInfoLabelOn        = False		; turn off info label
  sgres@cnLinesOn            = False		; draw contour lines
  sgres@cnLineLabelsOn       = False		; draw contour labels
  sgres@cnFillScaleF         = 0.7		; add extra density
  sgres@cnFillDotSizeF       = 0.002

  sgres@gsnAddCyclic         = True

; activate if gray shading for B&W plot  
  sgres@cnFillOn             = True
  sgres@cnFillColors         = (/"transparent","transparent"/) ; choose one color for our single cn level
  sgres@cnLevelSelectionMode = "ExplicitLevels"	         ; set explicit contour levels
  sgres@cnLevels             = 95.0                            ; only set one level
  sgres@lbLabelBarOn         = False

  sgres@tiMainString         = ""     ; title
  sgres@gsnCenterString      = ""  ; subtitle
  sgres@gsnLeftString        = ""    ; upper-left subtitle
  sgres@gsnRightString       = ""   ; upper-right subtitle

  sig_plot0 = gsn_csm_contour(wks,alpha(0,:,:),sgres)
  sig_plot1 = gsn_csm_contour(wks,alpha(1,:,:),sgres)
  sig_plot2 = gsn_csm_contour(wks,alpha(2,:,:),sgres)
  sig_plot3 = gsn_csm_contour(wks,alpha(3,:,:),sgres)
  sig_plot4 = gsn_csm_contour(wks,alpha(4,:,:),sgres)
  sig_plot5 = gsn_csm_contour(wks,alpha(5,:,:),sgres)

  opt                  = True
  opt@gsnShadeFillType = "pattern"
  ;opt@gsnShadeHigh     = 17
  opt@gsnShadeLow      = 17
  
  sig_plot0 = gsn_contour_shade(sig_plot0,95.0,-999,opt)
  sig_plot1 = gsn_contour_shade(sig_plot1,95.0,-999,opt)
  sig_plot2 = gsn_contour_shade(sig_plot2,95.0,-999,opt)
  sig_plot3 = gsn_contour_shade(sig_plot3,95.0,-999,opt)
  sig_plot4 = gsn_contour_shade(sig_plot4,95.0,-999,opt)
  sig_plot5 = gsn_contour_shade(sig_plot5,95.0,-999,opt)

  overlay(plot(0),sig_plot0)
  overlay(plot(1),sig_plot1)
  overlay(plot(2),sig_plot2)
  overlay(plot(3),sig_plot3)
  overlay(plot(4),sig_plot4)
  overlay(plot(5),sig_plot5)
  



 


;************************************************
; create panel
;************************************************
  resP                           = True                ; modify the panel plot
  resP@gsnPanelMainString        = ""
  resP@gsnPanelLabelBar          = True                ; add common colorbar
  resP@lbLabelFontHeightF        = 0.007               ; make labels smaller
  resP@lbLabelFont               = "times-roman"
  resP@lbTitleOn                 = True
  resP@lbLabelStride             = 4
  resP@lbTitleString             = "mm ~NN~ day~S~-1" ;"kJ/cm^2"
  resP@lbTitleFontHeightF        = 0.008
  resP@lbTitleFont               = "times-roman"
  resP@lbTitlePosition           = "Bottom"
  resP@pmLabelBarOrthogonalPosF  = -0.1
  resP@pmLabelBarWidthF            = 0.4                                         
  resP@pmLabelBarHeightF           = 0.05 
  ;resP@lbOrientation              = "Vertical"     ; vertical label bar
  resP@gsnPanelFigureStringsFont = "times-roman"
  resP@gsnPanelBottom            = 0.05                 ; add space at bottom
; resP@gsnMaximize               = True                 ; use full page
  resP@amJust   	          = "TopRight"
  resP@pmLabelBarOrthogonalPosF = -.03
  resP@pmLabelBarParallelPosF = .04
  resP@lbLabelFontHeightF  = 0.015     ; make labels smaller
  resP@lbTitleFontHeightF   =0.015
  resP@gsnPanelYWhiteSpacePercent = 2.5
  resP@gsnPanelXWhiteSpacePercent = 4.5
;  resP@txString   = "Temperature (~S~o~N~C)(model climatology - year 41 to 100)"
  resP@gsnMaximize      = True
  resP@gsnPaperOrientation = "portrait"


; resP@gsnPanelFigureStrings= (/Label_Global_mean_percent(0), Label_Global_mean_percent(1),Label_Global_mean_percent(2), Label_Global_mean_percent(3), Label_Global_mean_percent(4), Label_Global_mean_percent(5)/) ; add strings to panel
  resP@gsnPanelFigureFontHeightF = -0.1


  gsn_panel(wks,plot,(/3,2/),resP)               ; now draw as one plot
  ;draw(plot)
  ;frame(wks)
  exit()
  
end
  
  
 
