load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_code.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/contributed.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/shea_util.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/wrf/WRFUserARW.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/wrf/WRF_contributed.ncl"

begin
;*************************************************

; e_path="/home/<USER>/Documents/backup/my_work_draft/writeups/hydrological_cycle"
e_path="/home/<USER>/Documents/backup/my_work_draft/writeups/hydrological_cycle"
e_1CO2 = addfile(e_path+"/Yearly_E_CO2_01_100new1.nc", "r")
e_2CO2 = addfile(e_path+"/Yearly_E_2CO2_01_100new1.nc", "r")
e_UNIF_22_5 = addfile(e_path+"/Yearly_E2000_2CO2_UNIF_01_100_2D_1.nc", "r")
e_SOLAR = addfile("/home/<USER>/Documents/Data_solin/29_4_solin/E2000_solin_29_4/Yearly_E2000_solin_29_4_01_48_1.nc", "r")


; Get latitude and longitude
lat = e_1CO2->lat
lon = e_1CO2->lon



; Calculate surface temperature differences with area weighting
; First average over time dimension
TS_2CO2_time_avg = dim_avg_n_Wrap(e_2CO2->TS(40:99,:,:), 0)
TS_1CO2_time_avg = dim_avg_n_Wrap(e_1CO2->TS(40:99,:,:), 0)
TS_UNIF_22_5_time_avg = dim_avg_n_Wrap(e_UNIF_22_5->TS(40:99,:,:), 0)
TS_SOLAR_time_avg = dim_avg_n_Wrap(e_SOLAR->TS(40:47,:,:), 0)

PT_2CO2_time_avg = dim_avg_n_Wrap(e_2CO2->PRECT(40:99,:,:), 0)* 86400.0 
PT_1CO2_time_avg = dim_avg_n_Wrap(e_1CO2->PRECT(40:99,:,:), 0)* 86400.0 
PT_UNIF_22_5_time_avg = dim_avg_n_Wrap(e_UNIF_22_5->PRECT(40:99,:,:), 0)* 86400.0 
PT_SOLAR_time_avg = dim_avg_n_Wrap(e_SOLAR->PRECT(40:47,:,:), 0)* 86400.0 

; Compute zonal means (longitude-weighted average at each latitude)
rad = 4.0*atan(1.0)/180.0  ; Convert degrees to radians
clat = cos(lat * rad)   ; latitude weights (not used in this simplified version)

pt_ts_1co2=PT_1CO2_time_avg/TS_1CO2_time_avg
pt_ts_2co2=PT_2CO2_time_avg/TS_2CO2_time_avg
pt_ts_unif=PT_UNIF_22_5_time_avg/TS_UNIF_22_5_time_avg
pt_ts_solar=PT_SOLAR_time_avg/TS_SOLAR_time_avg

pt_ts_1co2_avg=dim_avg_n_Wrap(pt_ts_1co2,1)
pt_ts_2co2_avg=dim_avg_n_Wrap(pt_ts_2co2,1)
pt_ts_unif_avg=dim_avg_n_Wrap(pt_ts_unif,1)
pt_ts_solar_avg=dim_avg_n_Wrap(pt_ts_solar,1)

printVarSummary(pt_ts_1co2_avg)
printVarSummary(pt_ts_2co2_avg)
printVarSummary(pt_ts_unif_avg) 
printVarSummary(pt_ts_solar_avg)
; Create a multi-dimensional array for temperature plotting
tempPTPlotData = new((/4, dimsizes(lat)/), float)
tempPTPlotData(0,:) = pt_ts_1co2_avg  ; 2CO2 
tempPTPlotData(1,:) = pt_ts_2co2_avg  ; 1CO2
tempPTPlotData(2,:) = pt_ts_unif_avg  ; UNIF 
tempPTPlotData(3,:) = pt_ts_solar_avg  ; SOLAR

print(tempPTPlotData(2,:))
; Set up plot resources
wks = gsn_open_wks("pdf","PTperK_vs_lat_profiles2_absolute")

res = True
res@gsnDraw           = True
res@gsnFrame          = True
res@xyLineColors      = (/"black","red","blue","green"/)
res@xyLineThicknesses = (/2.5, 2.5, 2.5/)
res@xyDashPatterns    = (/0, 0, 0/)         ; all solid lines
res@gsnXYTopLabel     = False
res@tiMainString      = "Precipitation per unit warming vs Latitude"
res@tiYAxisString     = "SurfaceTemperature (K)"
res@tiXAxisString     = "Latitude (~F34~0~F~)"
res@trXMinF           = -90
res@trXMaxF           = 90

res@vpHeightF         = 0.6
res@vpWidthF          = 0.75

res@lgLabelFontHeightF = 0.015
res@xyExplicitLegendLabels = (/"1xCO~B~2 ", "1xCO~B~2", "SAI", "SOLAR"/)
res@pmLegendDisplayMode = "Always"
res@pmLegendSide = "Top"     ; or "Bottom", "Top", etc.
res@pmLegendParallelPosF = 0.8 ; fine-tune positioning
res@pmLegendWidthF         =  0.25                       ;-- define legend width
res@pmLegendHeightF        =  0.15
res@pmLegendOrthogonalPosF = -0.40
res@lgPerimOn = True           ; legend box border

; Create the plot
plot = gsn_csm_xy(wks, lat, tempPTPlotData, res)

exit()

end



