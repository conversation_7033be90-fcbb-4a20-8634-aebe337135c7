load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_code.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/contributed.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/shea_util.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/wrf/WRFUserARW.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/wrf/WRF_contributed.ncl"

begin
undf =-999.999
;file_names
case=(/"E2000C5_co2_01_100_1","E2000C5_2co2_01_100_1", "E2000_30mt_38hpa_01_100_1", "E2000_30mt_72hpa_01_100_1", "E2000_30mt_103hpa_01_100_1","E2000_35mt_103hpa_01_100_1"/)

pc=new((/6,60,96,144/),"float",undf)
pl=new((/6,60,96,144/),"float",undf)
PT=new((/6,60,96,144/),"float",undf)
dummy=new((/6,60,96,144/),"float",undf)
landsea=new((/6,60,96,144/),"float",undf)
PT_masked=new((/6,60,96,144/),"float",undf)

pc1=new((/6,60,96,144/),"float",undf)
pl1=new((/6,60,96,144/),"float",undf)
PT1=new((/6,60,96,144/),"float",undf)
dummy=new((/6,60,96,144/),"float",undf)
landsea1=new((/6,60,96,144/),"float",undf)
PT_masked1=new((/6,60,96,144/),"float",undf)

NAf_PT_masked=new((/6,60/),"float",undf)
NA_PT_masked=new((/6,60/),"float",undf)
SAs_PT_masked=new((/6,60/),"float",undf)
SAf_PT=new((/6,60/),"float",undf)
SA_PT=new((/6,60/),"float",undf)
AUS_PT=new((/6,60/),"float",undf)
TMI_masked=new((/6,60/),"float",undf)
NHMI_masked=new((/6,60/),"float",undf)
SHMI_masked=new((/6,60/),"float",undf)

do i= 0,dimsizes(case)-1
var1="JJA_Yearly"
var2="DJF_Yearly"
file_path="/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/JJA/"
file_path1="/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/DJF/"

; NHMI
fname= var1+"_"+case(i)+".nc"
fils =  systemfunc("ls "+file_path+fname)
s    = addfile(fils,"r")
pc(i,:,:,:)=s->PRECC(40:99,:,:)
pl(i,:,:,:)=s->PRECC(40:99,:,:)
PT(i,:,:,:)=((s->PRECC(40:99,:,:))+(s->PRECL(40:99,:,:)))*(8.64e+7)
; PT(i,:,:,:)= (s->PRECC(40:99,:,:)*8.64e+7)+(s->PRECL(40:99,:,:)*8.64e+7)
dummy(i,:,:,:)=s->PRECC(40:99,:,:)
copy_VarCoords(dummy, PT)
landsea(i,:,:,:)         = s->LANDFRAC(40:99,:,:)   
printVarSummary(landsea)
PT_masked= where(landsea.gt.0,PT,undf)
copy_VarCoords(dummy, PT_masked)

; North African monsoon-------------------------------------------------NAf
NAf_PT_masked(i,:)=dim_avg_n_Wrap(PT_masked(i,:,{5:20},{-20:40}),(/1,2/)) 

; North American monsoon-------------------------------------------------NA
NA_PT_masked(i,:)=dim_avg_n_Wrap(PT_masked(i,:,{0:30},{240:300}),(/1,2/))

;South Asian monsoon-------------------------------------------------SAs
SAs_PT_masked(i,:)=dim_avg_n_Wrap(PT_masked(i,:,{5:35},{60:110}),(/1,2/)) 


; SHMI
fname1= var2+"_"+case(i)+".nc"
fils1 =  systemfunc("ls "+file_path1+fname1)
s1    = addfile(fils1,"r")
pc1(i,:,:,:)=s1->PRECC(40:99,:,:)
pl1(i,:,:,:)=s1->PRECC(40:99,:,:)
PT1(i,:,:,:)=((s1->PRECC(40:99,:,:))+(s1->PRECL(40:99,:,:)))*(8.64e+7)
; PT(i,:,:,:)= (s->PRECC(40:99,:,:)*8.64e+7)+(s->PRECL(40:99,:,:)*8.64e+7)
dummy(i,:,:,:)=s1->PRECC(40:99,:,:)
copy_VarCoords(dummy, PT1)
landsea1(i,:,:,:)         = s1->LANDFRAC(40:99,:,:)   
; printVarSummary(landsea)
PT_masked1= where(landsea1.gt.0,PT1,undf)
copy_VarCoords(dummy, PT_masked1)

SAf_PT(i,:)=dim_avg_n_Wrap(PT_masked1(i,:,{-35:-5},{10:50}),(/1,2/)) 

; North American monsoon-------------------------------------------------SA
SA_PT(i,:)=dim_avg_n_Wrap(PT_masked1(i,:,{-35:-5},{280:330}),(/1,2/))

;South Asian monsoon-------------------------------------------------AUS
AUS_PT(i,:)=dim_avg_n_Wrap(PT_masked1(i,:,{-25:-5},{110:155}),(/1,2/)) 


NHMI_masked(i,:)=(NAf_PT_masked(i,:)+NA_PT_masked(i,:)+SAs_PT_masked(i,:))/3
SHMI_masked(i,:)=(SAf_PT(i,:)+SA_PT(i,:)+AUS_PT(i,:))/3
TMI_masked(i,:)=((NAf_PT_masked(i,:)+NA_PT_masked(i,:)+SAs_PT_masked(i,:)+SAf_PT(i,:)+SA_PT(i,:)+AUS_PT(i,:)))/6
end do
; printVarSummary(PT)
; printVarSummary(NAf_PT)
printVarSummary(TMI_masked)
; print(NHMI_masked(0,:))
a=dim_avg_n(TMI_masked(0,:),0)
b=dim_avg_n(TMI_masked(1,:),0)

print(a)
print(b)

asciiwrite ("TMI_1CO2_masked_C5.txt" ,  TMI_masked(0,:))
asciiwrite ("TMI_2CO2_masked_C5.txt" ,  TMI_masked(1,:))
asciiwrite ("TMI_30mt_38hpa_masked_C5.txt" , TMI_masked(2,:))
asciiwrite ("TMI_30mt_72hpa_masked_C5.txt" , TMI_masked(3,:))
asciiwrite ("TMI_30mt_103hpa_masked_C5.txt" ,  TMI_masked(4,:))
asciiwrite ("TMI_35mt_103hpa_masked_C5.txt" ,  TMI_masked(5,:))
end
