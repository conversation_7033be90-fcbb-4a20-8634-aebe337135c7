load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_code.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/contributed.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/shea_util.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/wrf/WRFUserARW.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/wrf/WRF_contributed.ncl"

begin
undf =-999.999
;file_names
; case=(/"CO2_01_100_2D_1","2CO2_01_100_2D_1", "2CO2_UNIF_01_100_2D_1", "2CO2_TROP_01_100_2D_1", "2CO2_POLAR_01_100_2D_1","2CO2_ARCTIC_01_100_2D_1", "2CO2_ANTARC_01_100_2D_1"/)
case=(/"E2000C5_co2_01_100_1","E2000C5_2co2_01_100_1", "E2000_30mt_38hpa_01_100_1", "E2000_30mt_72hpa_01_100_1", "E2000_30mt_103hpa_01_100_1","E2000_35mt_103hpa_01_100_1"/)
pc=new((/6,60,96,144/),"float",undf)
pl=new((/6,60,96,144/),"float",undf)
PT=new((/6,60,96,144/),"float",undf)
dummy=new((/6,60,96,144/),"float",undf)
landsea=new((/6,60,96,144/),"float",undf)
PT_masked=new((/6,60,96,144/),"float",undf)
Global_PT_masked=new((/6,60/),"float",undf)
Global_PT=new((/6,60/),"float",undf)
Global_PT_masked1=new((/6/),"float",undf)


do i= 0,dimsizes(case)-1
var1="Yearly"
file_path="/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/Yearly/"
; file_path="/Volumes/anuhdd2/Data/22_5MT/Yearly_som/"
fname= var1+"_"+case(i)+".nc"
fils =  systemfunc("ls "+file_path+fname)
s    = addfile(fils,"r")
pc(i,:,:,:)=s->PRECC(40:99,:,:)
pl(i,:,:,:)=s->PRECC(40:99,:,:)
PT(i,:,:,:)=((s->PRECC(40:99,:,:))+(s->PRECL(40:99,:,:)))*(8.64e+7)
; PT(i,:,:,:)= (s->PRECC(40:99,:,:)*8.64e+7)+(s->PRECL(40:99,:,:)*8.64e+7)
dummy(i,:,:,:)=s->PRECC(40:99,:,:)
copy_VarCoords(dummy, PT)
landsea(i,:,:,:)         = s->LANDFRAC(40:99,:,:)   
printVarSummary(landsea)
PT_masked= where(landsea.gt.0,PT,undf)
copy_VarCoords(dummy, PT_masked)

lat=s->lat
lon=s->lon


  jlat  = dimsizes( lat )
  rad    = 4.0*atan(1.0)/180.0
  re     = 6371220.0
  rr     = re*rad
  dlon   = abs(lon(2)-lon(1))*rr
  dx    = dlon*cos(lat*rad)
  dy     = new ( jlat, typeof(dx))
  dy(0)  = abs(lat(2)-lat(1))*rr
  dy(1:jlat-2)  = abs(lat(2:jlat-1)-lat(0:jlat-3))*rr*0.5   
  dy(jlat-1)    = abs(lat(jlat-1)-lat(jlat-2))*rr
  area   = dx*dy
  clat   = cos(lat*rad)
; Global precipitation af-------------------------------------------------NAf
; Global_PT_masked(i,:)=dim_avg_n_Wrap(PT_masked(i,:,:,:),(/1,2/)) 

; Global_PT_masked(i,:)=dim_avg_n_Wrap(PT_masked(i,:,:,:),(/1,2/)) 
; Global_PT_masked1(i)=dim_avg_n_Wrap(Global_PT_masked(i,:),(/0/)) 
; without masking
; Global_PT(i,:)=dim_avg_n_Wrap(PT(i,:,:,:),(/1,2/)) 
; Global_PT(i)=dim_avg_n_Wrap(Global_PT(i,:),(/0/)) 
; Global_PT(i,:)=dim_avg_n_Wrap(PT(i,:,:,:),(/1,2/)) 
Global_PT(i,:)=(wgt_areaave_Wrap(PT(i,:,:,:),area,1.0,0))
end do
; print(Global_PT(0,:))
a=dim_avg_n(Global_PT(0,:),0)
b=dim_avg_n(Global_PT(1,:),0)

print(a)
print(b)
; print(Global_PT_masked1)
; asciiwrite ("Global_PT_1CO2_masked.txt" ,  Global_PT_masked(0,:))
; asciiwrite ("Global_PT_2CO2_masked.txt" ,  Global_PT_masked(1,:))
; asciiwrite ("Global_PT_UNIF_22_5_masked.txt" , Global_PT_masked(2,:))
; asciiwrite ("Global_PT_TROP_22_5_masked.txt" , Global_PT_masked(3,:))
; asciiwrite ("Global_PT_POLA_22_5_masked.txt" ,  Global_PT_masked(4,:))
; asciiwrite ("Global_PT_ARCT_22_5_masked.txt" ,  Global_PT_masked(5,:))
; asciiwrite ("Global_PT_ANTA_22_5_masked.txt" ,  Global_PT_masked(6,:))

; asciiwrite ("Global_PT_1CO2.txt" ,  Global_PT(0,:))
; asciiwrite ("Global_PT_2CO2.txt" ,  Global_PT(1,:))
; asciiwrite ("Global_PT_UNIF_22_5.txt" , Global_PT(2,:))
; asciiwrite ("Global_PT_TROP_22_5.txt" , Global_PT(3,:))
; asciiwrite ("Global_PT_POLA_22_5.txt" ,  Global_PT(4,:))
; asciiwrite ("Global_PT_ARCT_22_5.txt" ,  Global_PT(5,:))
; asciiwrite ("Global_PT_ANTA_22_5.txt" ,  Global_PT(6,:))


; asciiwrite ("Global_PT_1CO2.txt" ,  Global_PT(0,:))
; asciiwrite ("Global_PT_2CO2.txt" ,  Global_PT(1,:))
; asciiwrite ("Global_PT_UNIF_22_5.txt" , Global_PT(2,:))
; asciiwrite ("Global_PT_TROP_22_5.txt" , Global_PT(3,:))
; asciiwrite ("Global_PT_POLA_22_5.txt" ,  Global_PT(4,:))
; asciiwrite ("Global_PT_ARCT_22_5.txt" ,  Global_PT(5,:))
; asciiwrite ("Global_PT_ANTA_22_5.txt" ,  Global_PT(6,:))

asciiwrite ("Global_PT_1CO2_C5.txt" ,  Global_PT(0,:))
asciiwrite ("Global_PT_2CO2_C5.txt" ,  Global_PT(1,:))
asciiwrite ("Global_PT_30mt_38hpa.txt" , Global_PT(2,:))
asciiwrite ("Global_PT_30mt_72hpa.txt" , Global_PT(3,:))
asciiwrite ("Global_PT_30mt_103hpa.txt" ,  Global_PT(4,:))
asciiwrite ("Global_PT_35mt_103hpa.txt" ,  Global_PT(5,:))
end