load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_code.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/contributed.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/shea_util.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/wrf/WRFUserARW.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/wrf/WRF_contributed.ncl"
begin
undf =-999.999
;file_names
case=(/"E2000C5_co2_01_100_1","E2000C5_2co2_01_100_1", "E2000_30mt_38hpa_01_100_1", "E2000_30mt_72hpa_01_100_1", "E2000_30mt_103hpa_01_100_1","E2000_35mt_103hpa_01_100_1"/)
pc=new((/6,60,96,144/),"float",undf)
pl=new((/6,60,96,144/),"float",undf)
PT=new((/6,60,96,144/),"float",undf)
dummy=new((/6,60,96,144/),"float",undf)
landsea=new((/6,60,96,144/),"float",undf)
PT_masked=new((/6,60,96,144/),"float",undf)

NAf_PT_masked=new((/6,60/),"float",undf)
NA_PT_masked=new((/6,60/),"float",undf)
SAs_PT_masked=new((/6,60/),"float",undf)
NHMI_masked=new((/6,60/),"float",undf)

do i= 0,dimsizes(case)-1
var1="JJA_Yearly"
file_path="/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/JJA/"
; file_path="/Volumes/anuhdd2/Data/22_5MT/Yearly_som/"
fname= var1+"_"+case(i)+".nc"
fils =  systemfunc("ls "+file_path+fname)
s    = addfile(fils,"r")
pc(i,:,:,:)=s->PRECC(40:99,:,:)
pl(i,:,:,:)=s->PRECC(40:99,:,:)
PT(i,:,:,:)=((s->PRECC(40:99,:,:))+(s->PRECL(40:99,:,:)))*(8.64e+7)
; PT(i,:,:,:)= (s->PRECC(40:99,:,:)*8.64e+7)+(s->PRECL(40:99,:,:)*8.64e+7)
dummy(i,:,:,:)=s->PRECC(40:99,:,:)
copy_VarCoords(dummy, PT)
landsea(i,:,:,:)         = s->LANDFRAC(40:99,:,:)   
printVarSummary(landsea)
PT_masked= where(landsea.gt.0,PT,undf)
copy_VarCoords(dummy, PT_masked)

; North African monsoon-------------------------------------------------NAf
NAf_PT_masked(i,:)=dim_avg_n_Wrap(PT_masked(i,:,{5:20},{-20:40}),(/1,2/)) 

; North American monsoon-------------------------------------------------NA
NA_PT_masked(i,:)=dim_avg_n_Wrap(PT_masked(i,:,{0:30},{240:300}),(/1,2/))

;South Asian monsoon-------------------------------------------------SAs
SAs_PT_masked(i,:)=dim_avg_n_Wrap(PT_masked(i,:,{5:35},{60:110}),(/1,2/)) 

NHMI_masked(i,:)=((NAf_PT_masked(i,:)+NA_PT_masked(i,:)+SAs_PT_masked(i,:)))/3
end do
; printVarSummary(PT)
; printVarSummary(NAf_PT)
printVarSummary(NHMI_masked)
a=dim_avg_n(NHMI_masked(0,:),0)
b=dim_avg_n(NHMI_masked(1,:),0)

print(a)
print(b)
asciiwrite ("NAf_1CO2_masked_C5.txt" ,  NAf_PT_masked(0,:))
asciiwrite ("NAf_2CO2_masked_C5.txt" , NAf_PT_masked(1,:))
asciiwrite ("NAf_30MT_38hpa_masked_C5.txt" ,  NAf_PT_masked(2,:))
asciiwrite ("NAf_30MT_72hpa_masked_C5.txt" , NAf_PT_masked(3,:))
asciiwrite ("NAf_30MT_103hpa_masked_C5.txt" , NAf_PT_masked(4,:))
asciiwrite ("NAf_35MT_103hpa_masked_C5.txt" ,  NAf_PT_masked(5,:))

asciiwrite ("NA_1CO2_masked_C5.txt" ,  NA_PT_masked(0,:))
asciiwrite ("NA_2CO2_masked_C5.txt" ,  NA_PT_masked(1,:))
asciiwrite ("NA_30MT_38hpa_masked_C5.txt" ,  NA_PT_masked(2,:))
asciiwrite ("NA_30MT_72hpa_masked_C5.txt" ,  NA_PT_masked(3,:))
asciiwrite ("NA_30MT_103hpa_masked_C5.txt" ,  NA_PT_masked(4,:))
asciiwrite ("NA_35MT_103hpa_masked_C5.txt" ,  NA_PT_masked(5,:))

asciiwrite ("SAs_1CO2_masked_C5.txt" ,  SAs_PT_masked(0,:))
asciiwrite ("SAs_2CO2_masked_C5.txt" ,  SAs_PT_masked(1,:))
asciiwrite ("SAs_30MT_38hpa_masked_C5.txt" ,  SAs_PT_masked(2,:))
asciiwrite ("SAs_30MT_72hpa_masked_C5.txt" ,  SAs_PT_masked(3,:))
asciiwrite ("SAs_30MT_103hpa_masked_C5.txt" ,  SAs_PT_masked(4,:))
asciiwrite ("SAs_35MT_103hpa_masked_C5.txt" ,  SAs_PT_masked(5,:))

; asciiwrite ("NHMI_1CO2_masked.txt" , sprintf("%9.3f", NHMI_masked(0,:)))
; asciiwrite ("NHMI_2CO2_masked.txt" , sprintf("%9.3f", NHMI_masked(1,:)))
; asciiwrite ("NHMI_UNIF_22_5_masked.txt" , sprintf("%9.3f", NHMI_masked(2,:)))
; asciiwrite ("NHMI_TROP_22_5_masked.txt" , sprintf("%9.3f", NHMI_masked(3,:)))
; asciiwrite ("NHMI_POLA_22_5_masked.txt" , sprintf("%9.3f", NHMI_masked(4,:)))
; asciiwrite ("NHMI_ARCT_22_5_masked.txt" , sprintf("%9.3f", NHMI_masked(5,:)))
; asciiwrite ("NHMI_ANTA_22_5_masked.txt" , sprintf("%9.3f", NHMI_masked(6,:)))

asciiwrite ("NHMI_1CO2_masked_C5.txt" ,  NHMI_masked(0,:))
asciiwrite ("NHMI_2CO2_masked_C5.txt" ,  NHMI_masked(1,:))
asciiwrite ("NHMI_30MT_38hpa_masked_C5.txt" , NHMI_masked(2,:))
asciiwrite ("NHMI_30MT_72hpa_masked_C5.txt" , NHMI_masked(3,:))
asciiwrite ("NHMI_30MT_103hpa_masked_C5.txt" ,  NHMI_masked(4,:))
asciiwrite ("NHMI_35MT_103hpa_masked_C5.txt" ,  NHMI_masked(5,:))

; asciiwrite ("NHMI_1CO2_masked3.txt" , sprintf("%9.3f",  NHMI_masked(0,:)))
; asciiwrite ("NHMI_2CO2_masked3.txt", sprintf("%9.3f",  NHMI_masked(1,:)))
; asciiwrite ("NHMI_UNIF_22_5_masked3.txt" , sprintf("%9.3f", NHMI_masked(2,:)))
; asciiwrite ("NHMI_TROP_22_5_masked3.txt", sprintf("%9.3f", NHMI_masked(3,:)))
; asciiwrite ("NHMI_POLA_22_5_masked3.txt", sprintf("%9.3f",  NHMI_masked(4,:)))
; asciiwrite ("NHMI_ARCT_22_5_masked3.txt" , sprintf("%9.3f",  NHMI_masked(5,:)))
; asciiwrite ("NHMI_ANTA_22_5_masked3.txt" , sprintf("%9.3f",  NHMI_masked(6,:)))
; print(NHMI_masked(2,:))
end
