load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_code.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/contributed.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/shea_util.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/wrf/WRFUserARW.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/wrf/WRF_contributed.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/esmf/ESMF_regridding.ncl"
load "/home/<USER>/Documents/backup/Neethu_IISc/Programs/India_shapefile/shapefile_utils.ncl"       ;-- down load the file and save it in current dir 
begin
undf =-999.999
e_path="/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/JJAS/"
 

undf = -999.999
; Set file path
; e_path = "/home/<USER>/Desktop/Data/JJAS/"

; Open file and extract variables
e_1CO2 = addfile(e_path + "JJAS_Yearly_E2000C5_co2_01_100_1.nc", "r")
e_2CO2 = addfile(e_path + "JJAS_Yearly_E2000C5_2co2_01_100_1.nc", "r")
e_30mt_38hpa = addfile(e_path + "JJAS_Yearly_E2000_30mt_38hpa_01_100_1.nc", "r")
e_30mt_72hpa = addfile(e_path + "JJAS_Yearly_E2000_30mt_72hpa_01_100_1.nc", "r")
e_30mt_103hpa = addfile(e_path + "JJAS_Yearly_E2000_30mt_103hpa_01_100_1.nc", "r")
e_35mt_103hpa = addfile(e_path + "JJAS_Yearly_E2000_35mt_103hpa_01_100_1.nc", "r")

PRECC_e_1CO2 = e_1CO2->PRECC(40:99, :, :)
PRECL_e_1CO2 = e_1CO2->PRECL(40:99, :, :)
PRECC_e_2CO2 = e_2CO2->PRECC(40:99, :, :)
PRECL_e_2CO2 = e_2CO2->PRECL(40:99, :, :)
PRECC_e_30mt_38hpa = e_30mt_38hpa->PRECC(40:99, :, :)
PRECL_e_30mt_38hpa = e_30mt_38hpa->PRECL(40:99, :, :)
PRECC_e_30mt_72hpa = e_30mt_72hpa->PRECC(40:99, :, :)
PRECL_e_30mt_72hpa = e_30mt_72hpa->PRECL(40:99, :, :)
PRECC_e_30mt_103hpa = e_30mt_103hpa->PRECC(40:99, :, :)
PRECL_e_30mt_103hpa = e_30mt_103hpa->PRECL(40:99, :, :)
PRECC_e_35mt_103hpa = e_35mt_103hpa->PRECC(40:99, :, :)
PRECL_e_35mt_103hpa = e_35mt_103hpa->PRECL(40:99, :, :)

P_e_1CO2 = (PRECC_e_1CO2 * 8.64e+7) + (PRECL_e_1CO2 * 8.64e+7)
P_e_2CO2 = (PRECC_e_2CO2 * 8.64e+7) + (PRECL_e_2CO2 * 8.64e+7)
P_e_30mt_38hpa = (PRECC_e_30mt_38hpa * 8.64e+7) + (PRECL_e_30mt_38hpa * 8.64e+7)
P_e_30mt_72hpa = (PRECC_e_30mt_72hpa * 8.64e+7) + (PRECL_e_30mt_72hpa * 8.64e+7)
P_e_30mt_103hpa = (PRECC_e_30mt_103hpa * 8.64e+7) + (PRECL_e_30mt_103hpa * 8.64e+7)
P_e_35mt_103hpa = (PRECC_e_35mt_103hpa * 8.64e+7) + (PRECL_e_35mt_103hpa * 8.64e+7)

; Set fill value and copy variable coordinates
dummy=e_1CO2->PRECC(0:5,:,:)
dummy@_FillValue =-999.999


P_mean=new((/6,96,144/),typeof(dummy),dummy@_FillValue)
P_mean(0,:,:)=dim_avg_n_Wrap(P_e_2CO2,0)
P_mean(1,:,:)=dim_avg_n_Wrap(P_e_1CO2,0)
P_mean(2,:,:)=dim_avg_n_Wrap(P_e_30mt_38hpa,0)
P_mean(3,:,:)=dim_avg_n_Wrap(P_e_30mt_72hpa,0)
P_mean(4,:,:)=dim_avg_n_Wrap(P_e_30mt_103hpa,0)
P_mean(5,:,:)=dim_avg_n_Wrap(P_e_35mt_103hpa,0)
copy_VarCoords(dummy,P_mean)
printVarSummary(P_mean)

P_variance=new((/6,96,144/),typeof(dummy),dummy@_FillValue)
P_variance(0,:,:)=dim_variance_n_Wrap(P_e_2CO2,0)
P_variance(1,:,:)=dim_variance_n_Wrap(P_e_1CO2,0)
P_variance(2,:,:)=dim_variance_n_Wrap(P_e_30mt_38hpa,0)
P_variance(3,:,:)=dim_variance_n_Wrap(P_e_30mt_72hpa,0)
P_variance(4,:,:)=dim_variance_n_Wrap(P_e_30mt_103hpa,0)
P_variance(5,:,:)=dim_variance_n_Wrap(P_e_35mt_103hpa,0)
copy_VarCoords(dummy,P_variance)

P_std_dev=new((/6,96,144/),typeof(dummy),dummy@_FillValue)
P_std_dev(0,:,:)=dim_variance_n_Wrap(P_e_2CO2,0)
P_std_dev(1,:,:)=dim_variance_n_Wrap(P_e_1CO2,0)
P_std_dev(2,:,:)=dim_variance_n_Wrap(P_e_30mt_38hpa,0)
P_std_dev(3,:,:)=dim_variance_n_Wrap(P_e_30mt_72hpa,0)
P_std_dev(4,:,:)=dim_variance_n_Wrap(P_e_30mt_103hpa,0)
P_std_dev(5,:,:)=dim_variance_n_Wrap(P_e_35mt_103hpa,0)
copy_VarCoords(dummy,P_std_dev)
;---------------------------------------------------------------
dummy2=e_1CO2->PRECC(0:4,:,:)
dummy2@_FillValue =-999.999

Change_P=new((/5,96,144/),typeof(dummy2),dummy2@_FillValue)
Change_P(0,:,:)=P_mean(1,:,:)-P_mean(0,:,:)
Change_P(1,:,:)=P_mean(2,:,:)-P_mean(0,:,:)
Change_P(2,:,:)=P_mean(3,:,:)-P_mean(0,:,:)
Change_P(3,:,:)=P_mean(4,:,:)-P_mean(0,:,:)
Change_P(4,:,:)=P_mean(5,:,:)-P_mean(0,:,:)
copy_VarCoords(dummy2,Change_P)




prob_P=new((/5,96,144/),typeof(dummy),dummy@_FillValue)
prob_P(0,:,:)=ttest(P_mean(0,:,:),P_variance(0,:,:),60,P_mean(1,:,:),P_variance(1,:,:),60,False,False)
prob_P(1,:,:)=ttest(P_mean(0,:,:),P_variance(0,:,:),60,P_mean(2,:,:),P_variance(2,:,:),60,False,False)
prob_P(2,:,:)=ttest(P_mean(0,:,:),P_variance(0,:,:),60,P_mean(3,:,:),P_variance(3,:,:),60,False,False)
prob_P(3,:,:)=ttest(P_mean(0,:,:),P_variance(0,:,:),60,P_mean(4,:,:),P_variance(4,:,:),60,False,False)
prob_P(4,:,:)=ttest(P_mean(0,:,:),P_variance(0,:,:),60,P_mean(5,:,:),P_variance(5,:,:),60,False,False)
copy_VarCoords(dummy2,prob_P)

alpha=(1-prob_P)*100
copy_VarCoords(dummy2,alpha)

Change_sig=mask(Change_P,(alpha.ge.95),True) ;90% 
copy_VarCoords(dummy2,Change_sig)
 

landsea    = e_1CO2->LANDFRAC(0:4,:,:)
Land_mask_Change_P   = where(landsea.gt.0,Change_P,Change_P@_FillValue)
copy_VarCoords(dummy2,Land_mask_Change_P )

alpha      = where(landsea.gt.0,alpha,alpha@_FillValue)

;----------------------Add Mask for India
 
;shp_name  = "/Volumes/CAOS_BKUP_1/Geoengineering/NewAnalysis_Nov_2021/Precipitation/Spactial_change/Indian_Monsoon/IND_JK/india_bndry.shp" ;-- shapefile name
shp_name  = "/home/<USER>/Documents/backup/Neethu_IISc/Programs/India_shapefile/INDIA.shp" ;-- shapefile name
             
;-- Indian domain
   minlon = 60.
   maxlon = 110.
   minlat = 0.
   maxlat = 40

;-- resources for the shapefile_mask_data function
     opt = True
     opt@return_mask = True       ;-- this forces the return of a 0s and 1s mask array
     opt@debug       = True
     ;opt@minlon      = minlon     ; Makes the shapefile masking
     ;opt@maxlon      = maxlon     ; go faster.
     ;opt@minlat      = minlat
     ;opt@maxlat      = maxlat
    ;  printVarSummary(P_e_1CO2(0,:,:))
;-- create the mask based on the given shapefile; add coordinates
; India_masked  =  shapefile_mask_data(Change_P(0,:,:), shp_name, opt)
India_masked  =  shapefile_mask_data(P_mean(0,:,:), shp_name, opt)

lat=e_1CO2->lat
lon=e_1CO2->lon

     India_masked!0= "lat"
     India_masked!1= "lon"
     ; india_mask@lat=lat
     ; india_mask@lon=lon
     India_masked&lat=lat
     India_masked&lon=lon

INDIA=new((/5,96,144/),typeof(dummy2),dummy2@_FillValue)

do i=0,4
  INDIA(i,:,:)=India_masked
end do


;Change_P= mask(Change_P,(alpha.ge.95),True) ;90% 

mask_Change_P = where(INDIA.eq.1,Change_P,Change_P@_FillValue)
mask_alpha    = where(INDIA.eq.1,alpha,alpha@_FillValue)

copy_VarCoords(dummy2,mask_Change_P)
copy_VarCoords(dummy2,mask_alpha)
;******************************************                 Areal average
 jlat  = dimsizes( lat )
  rad    = 4.0*atan(1.0)/180.0
  re     = 6371220.0
  rr     = re*rad
  dlon   = abs(lon(2)-lon(1))*rr
  dx     = dlon*cos(lat*rad)
  dy     = new ( jlat, typeof(dx))                                                            ; close enough
  dy(0)  = abs(lat(2)-lat(1))*rr
  dy(1:jlat-2)  = abs(lat(2:jlat-1)-lat(0:jlat-3))*rr*0.5   
  dy(jlat-1)    = abs(lat(jlat-1)-lat(jlat-2))*rr
  area   = dx*dy                                ; cell area function of latitude only
  clat   = cos(lat*rad)

;****************************************************

;--------------------------------For Labels-------------------------------------------------------------------------------------   
Global_mean_percent=(wgt_areaave_Wrap(Change_P,area,1.0,0)/wgt_areaave_Wrap(P_mean(0,:,:),area,1.0,0))*100.
copy_VarCoords(dummy2,Global_mean_percent)
Label_Global_mean_percent=("Mean="+decimalPlaces(Global_mean_percent,2,True)+"%") 
;------------------------------------------------------------------------------------------------------------------------------- 
Change_sig&lat@units="degrees_north"
Change_sig&lon@units="degrees_east"  
;-------------------------------------------------------------------------------------------------------------------------------

print(area)
 



;----------------------
;-- create the mask based on the given shapefile; add coordinates


;---------------------------------------------------------------------------------------
;---------------------------------------------------------------------------------------
dummys=e_1CO2->PRECC(0:59,:,:)
dummys@_FillValue =-999.999


INDIA_years=new((/60,96,144/),typeof(dummys),dummys@_FillValue)
do i=0,59
  INDIA_years(i,:,:)=India_masked
end do

copy_VarCoords(dummys,INDIA_years)

India_masked_P_1CO2=new((/60,96,144/),typeof(dummys),dummys@_FillValue)
India_masked_P_2CO2=new((/60,96,144/),typeof(dummys),dummys@_FillValue)
India_masked_P_30mt_38hpa=new((/60,96,144/),typeof(dummys),dummys@_FillValue)
India_masked_P_30mt_72hpa=new((/60,96,144/),typeof(dummys),dummys@_FillValue)
India_masked_P_30mt_103hpa=new((/60,96,144/),typeof(dummys),dummys@_FillValue)
India_masked_P_35mt_103hpa=new((/60,96,144/),typeof(dummys),dummys@_FillValue)

do i=0,59
  India_masked_P_1CO2(i,:,:)=where(INDIA_years(i,:,:).eq.1,P_e_1CO2(i,:,:),dummys@_FillValue)
  India_masked_P_2CO2(i,:,:)=where(INDIA_years(i,:,:).eq.1,P_e_2CO2(i,:,:),dummys@_FillValue)
  India_masked_P_30mt_38hpa(i,:,:)=where(INDIA_years(i,:,:).eq.1,P_e_30mt_38hpa(i,:,:),dummys@_FillValue)
  India_masked_P_30mt_72hpa(i,:,:)=where(INDIA_years(i,:,:).eq.1,P_e_30mt_72hpa(i,:,:),dummys@_FillValue)
  India_masked_P_30mt_103hpa(i,:,:)=where(INDIA_years(i,:,:).eq.1,P_e_30mt_103hpa(i,:,:),dummys@_FillValue)
  India_masked_P_35mt_103hpa(i,:,:)=where(INDIA_years(i,:,:).eq.1,P_e_35mt_103hpa(i,:,:),dummys@_FillValue)
end do

copy_VarCoords(dummys,India_masked_P_1CO2)
copy_VarCoords(dummys,India_masked_P_2CO2)
copy_VarCoords(dummys,India_masked_P_30mt_38hpa)
copy_VarCoords(dummys,India_masked_P_30mt_72hpa)
copy_VarCoords(dummys,India_masked_P_30mt_103hpa)
copy_VarCoords(dummys,India_masked_P_35mt_103hpa)


mask_P_mean_1CO2=wgt_areaave_Wrap(India_masked_P_1CO2,area,1.0,0)
mask_P_mean_2CO2=wgt_areaave_Wrap(India_masked_P_2CO2,area,1.0,0)
mask_P_mean_30mt_38hpa=wgt_areaave_Wrap(India_masked_P_30mt_38hpa,area,1.0,0)
mask_P_mean_30mt_72hpa=wgt_areaave_Wrap(India_masked_P_30mt_72hpa,area,1.0,0)
mask_P_mean_30mt_103hpa=wgt_areaave_Wrap(India_masked_P_30mt_103hpa,area,1.0,0)
mask_P_mean_35mt_103hpa=wgt_areaave_Wrap(India_masked_P_35mt_103hpa,area,1.0,0)
 

; asciiwrite ("India_mask_P_mean_1CO2.txt" , sprintf("%9.3f", mask_P_mean_1CO2))
; asciiwrite ("India_mask_P_mean_2CO2.txt" , sprintf("%9.3f", mask_P_mean_2CO2))
; asciiwrite ("India_mask_P_mean_30mt_38hpa.txt" , sprintf("%9.3f", mask_P_mean_30mt_38hpa))
; asciiwrite ("India_mask_P_mean_30mt_72hpa.txt" , sprintf("%9.3f", mask_P_mean_30mt_72hpa))
; asciiwrite ("India_mask_P_mean_30mt_103hpa.txt" , sprintf("%9.3f", mask_P_mean_30mt_103hpa))
; asciiwrite ("India_mask_P_mean_35mt_103hpa.txt" , sprintf("%9.3f", mask_P_mean_35mt_103hpa))
; asciiwrite ("India_mask_P_mean_ANTA.txt" , sprintf("%9.3f", mask_P_mean_ANTA))
; print( mask_P_mean_30mt_38hpa)
asciiwrite ("India_mask_P_mean_1CO2_C5.txt" , mask_P_mean_1CO2)
asciiwrite ("India_mask_P_mean_2CO2_C5.txt" , mask_P_mean_2CO2)
asciiwrite ("India_mask_P_mean_30mt_38hpa.txt" , mask_P_mean_30mt_38hpa)
asciiwrite ("India_mask_P_mean_30mt_72hpa.txt" , mask_P_mean_30mt_72hpa)
asciiwrite ("India_mask_P_mean_30mt_103hpa.txt" , mask_P_mean_30mt_103hpa)
asciiwrite ("India_mask_P_mean_35mt_103hpa.txt" , mask_P_mean_35mt_103hpa)
a=dim_avg_n_Wrap(mask_P_mean_2CO2, 0)
print(a)
b=dim_avg_n_Wrap(mask_P_mean_1CO2, 0)
print(b)
exit()
; print(mask_P_mean_1CO2)
end