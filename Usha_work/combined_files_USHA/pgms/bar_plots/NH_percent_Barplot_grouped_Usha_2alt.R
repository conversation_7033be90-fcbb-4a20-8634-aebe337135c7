library(ggthemes)
# setwd("/Volumes/CAOS_2/Analysis/292.7/Figures/Sensitivity_Figures/ISMR_ERF_TG/CleanFigure")
library(ggplot2) # package for plotting
library(png)
library(ncdf4)
library('reshape2')
library(<PERSON><PERSON><PERSON><PERSON>)
library(ggrepel)
# library(ggpubr)
library(dplyr)
mav <- function(x,n=5){stats::filter(x,rep(1/n,n), sides=2)} #moving average fuction
standard_error <- function(x) sd(x) / sqrt(length(x)) # Create own function  (https://statisticsglobe.com/standard-error-in-r-example)


NA_1CO2 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/NA_1CO2_masked_C5.txt", header = FALSE)))
NA_2CO2 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/NA_2CO2_masked_C5.txt", header = FALSE))) #for just three points

NA_22_5_38 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/NA_30MT_38hpa_masked_C5.txt", header = FALSE)))
NA_22_5_72 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/NA_30MT_72hpa_masked_C5.txt", header = FALSE)))
NA_22_5_103 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/NA_30MT_103hpa_masked_C5.txt", header = FALSE)))
NA_22_5_103_35 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/NA_35MT_103hpa_masked_C5.txt", header = FALSE)))


NAf_1CO2 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/NAf_1CO2_masked_C5.txt", header = FALSE)))
NAf_2CO2 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/NAf_2CO2_masked_C5.txt", header = FALSE))) #for just three points

NAf_22_5_38 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/NAf_30MT_38hpa_masked_C5.txt", header = FALSE)))
NAf_22_5_72 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/NAf_30MT_72hpa_masked_C5.txt", header = FALSE)))
NAf_22_5_103 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/NAf_30MT_103hpa_masked_C5.txt", header = FALSE)))
NAf_22_5_103_35 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/NAf_35MT_103hpa_masked_C5.txt", header = FALSE)))


SAs_1CO2 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/SAs_1CO2_masked_C5.txt", header = FALSE)))
SAs_2CO2 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/SAs_2CO2_masked_C5.txt", header = FALSE))) #for just three points

SAs_22_5_38 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/SAs_30MT_38hpa_masked_C5.txt", header = FALSE)))
SAs_22_5_72 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/SAs_30MT_72hpa_masked_C5.txt", header = FALSE)))
SAs_22_5_103 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/SAs_30MT_103hpa_masked_C5.txt", header = FALSE)))
SAs_22_5_103_35 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/SAs_35MT_103hpa_masked_C5.txt", header = FALSE)))

# ###########################
# # ###########################
Change_NA_1CO2 = NA_2CO2-NA_1CO2
Change_NA_38 = NA_22_5_38-NA_1CO2
Change_NA_72 = NA_22_5_72-NA_1CO2
Change_NA_103 = NA_22_5_103-NA_1CO2
Change_NA_103_35 = NA_22_5_103_35-NA_1CO2

NA_mean=c((mean(Change_NA_1CO2/NA_1CO2)*100),
             (mean(Change_NA_38/NA_1CO2)*100),
      #       (mean(Change_NA_72/NA_1CO2)*100),
           (mean(Change_NA_103/NA_1CO2)*100),
             (mean(Change_NA_103_35/NA_1CO2)*100))
           
             print(NA_mean)
NA_SE=c(standard_error(Change_NA_1CO2/NA_1CO2)*100,
                                     standard_error(Change_NA_38/NA_1CO2)*100,
                               #     standard_error(Change_NA_72/NA_1CO2)*100,
                                    standard_error(Change_NA_103/NA_1CO2)*100,
                                     standard_error(Change_NA_103_35/NA_1CO2)*100)

Change_NAf_1CO2 = NAf_2CO2-NAf_1CO2
Change_NAf_38 = NAf_22_5_38-NAf_1CO2
Change_NAf_72 = NAf_22_5_72-NAf_1CO2
Change_NAf_103 = NAf_22_5_103-NAf_1CO2
Change_NAf_103_35 = NAf_22_5_103_35-NAf_1CO2

NAf_mean=c((mean(Change_NAf_1CO2/NAf_1CO2)*100),
             (mean(Change_NAf_38/NAf_1CO2)*100),
            # (mean(Change_NAf_72/NAf_1CO2)*100),
           (mean(Change_NAf_103/NAf_1CO2)*100),
             (mean(Change_NAf_103_35/NAf_1CO2)*100))
           
             print(NAf_mean)
NAf_SE=c(standard_error(Change_NAf_1CO2/NAf_1CO2)*100,
                                     standard_error(Change_NAf_38/NAf_1CO2)*100,
                                 #   standard_error(Change_NAf_72/NAf_1CO2)*100,
                                    standard_error(Change_NAf_103/NAf_1CO2)*100,
                                     standard_error(Change_NAf_103_35/NAf_1CO2)*100)

# Add SAs data calculations
Change_SAs_1CO2 = SAs_2CO2-SAs_1CO2
Change_SAs_38 = SAs_22_5_38-SAs_1CO2
Change_SAs_72 = SAs_22_5_72-SAs_1CO2
Change_SAs_103 = SAs_22_5_103-SAs_1CO2
Change_SAs_103_35 = SAs_22_5_103_35-SAs_1CO2

SAs_mean=c((mean(Change_SAs_1CO2/SAs_1CO2)*100),
             (mean(Change_SAs_38/SAs_1CO2)*100),
     #        (mean(Change_SAs_72/SAs_1CO2)*100),
           (mean(Change_SAs_103/SAs_1CO2)*100),
             (mean(Change_SAs_103_35/SAs_1CO2)*100))
           
             print(SAs_mean)
SAs_SE=c(standard_error(Change_SAs_1CO2/SAs_1CO2)*100,
                                     standard_error(Change_SAs_38/SAs_1CO2)*100,
                                  #  standard_error(Change_SAs_72/SAs_1CO2)*100,
                                    standard_error(Change_SAs_103/SAs_1CO2)*100,
                                     standard_error(Change_SAs_103_35/SAs_1CO2)*100)

# Create histogram data

#hist_data <- data.frame(
 # Region = rep(c("NA", "NAf", "SAs"), each = 5),
  #Experiment = rep(c("UNIFORM", "TROPIC", "POLAR"), 3),
 # Experiment = rep(c("CO2", "UNIFORM_30MT_38hpa", "UNIFORM_30MT_72hpa", "UNIFORM_30MT_103hpa", "UNIFORM_35MT_103hpa","CO2", "UNIFORM_30MT_38hpa", "UNIFORM_30MT_72hpa", "UNIFORM_30MT_103hpa", "UNIFORM_35MT_103hpa","CO2", "UNIFORM_30MT_38hpa", "UNIFORM_30MT_72hpa", "UNIFORM_30MT_103hpa", "UNIFORM_35MT_103hpa")),

  #Value = c((Change_NA_1CO2/NA_1CO2)*100,
    #(Change_NA_38/NA_1CO2)*100,
    #(Change_NA_72/NA_1CO2)*100,
   # (Change_NA_103/NA_1CO2)*100,
   # (Change_NA_103_35/NA_1CO2)*100,
   # (Change_NAf_1CO2/NAf_1CO2)*100,
   # (Change_NAf_38/NAf_1CO2)*100,
    #(Change_NAf_72/NAf_1CO2)*100,
    #(Change_NAf_103/NAf_1CO2)*100,
    #(Change_NAf_103_35/NAf_1CO2)*100,
   # (Change_SAs_1CO2/SAs_1CO2)*100,
   # (Change_SAs_38/SAs_1CO2)*100,
   # (Change_SAs_72/SAs_1CO2)*100,
   # (Change_SAs_103/SAs_1CO2)*100
   # (Change_SAs_103_35/SAs_1CO2)*100
 # )
#)
# Create histogram plot
#hist_colors <- c("UNIF" = "#A6CEE3", "TROP" = "#FB9A99", "POLA" = "#33A02C")

#p_hist <- ggplot(hist_data, aes(x = Value, fill = Experiment)) +
  #geom_histogram(alpha = 0.7, position = "identity", bins = 20) +
  #facet_grid(Region ~ Experiment, scales = "free") +
 # scale_fill_manual(values = hist_colors) +
 # labs(x = "Δ Precipitation (%)", y = "Frequency", 
 #      title = "Distribution of Precipitation Changes by Region and Experiment") +
 # theme_bw() +
 # theme(
  #  strip.text = element_text(family = "Times New Roman", size = 12),
  #  axis.text = element_text(family = "Times New Roman", size = 10),
 # axis.title = element_text(family = "Times New Roman", size = 14),
  #  plot.title = element_text(family = "Times New Roman", size = 16, hjust = 0.5),
  #  legend.position = "none"
 #) +
 #geom_vline(xintercept = 0, linetype = "dashed", alpha = 0.5)

#p_hist

#ggSAsve("Monsoon_regions_histogram_UNIF_TROP_POLA.png", width = 30, height = 20, units = "cm", dpi = 300)

df_all <- data.frame(
  Region = rep(c("North America", "North Africa", "South Asian"), each = 4),
  #Experiment = (c("UNIFORM", "TROPIC", "POLAR","UNIFORM", "TROPIC", "POLAR","UNIFORM", "TROPIC", "POLAR")),
    Experiment = (c("CO2","UNIFORM_30MT_38hpa", "UNIFORM_30MT_103hpa","UNIFORM_35MT_103hpa","CO2","UNIFORM_30MT_38hpa", "UNIFORM_30MT_103hpa","UNIFORM_35MT_103hpa","CO2","UNIFORM_30MT_38hpa", "UNIFORM_30MT_103hpa","UNIFORM_35MT_103hpa")),

  Cntl = rep("25.76Mt", 12),
  Mean = c(NA_mean, NAf_mean, SAs_mean),
  SE = c(NA_SE, NAf_SE, SAs_SE)
)
print(df_all)
#df_all$Experiment <- factor(df_all$Experiment, levels = c("CO2", "UNIFORM", "ARCTIC", "ANTARCTIC"))
df_all$Experiment <- factor(df_all$Experiment, levels = c("CO2",  "UNIFORM_30MT_38hpa",  "UNIFORM_30MT_103hpa","UNIFORM_35MT_103hpa"))

# Alternative approach if you want to keep 1XCO2 in the data frame
# df_all <- data.frame(
#   Region = c(rep("NA", 3), rep("NAf", 3), rep("SAs", 3)),
#   Experiment = rep(c("UNIFORM", "TROPIC", "POLAR"), 3),
#   Cntl = rep("25.76Mt", 9),
#   Mean = c(NA_mean, NAf_mean, SAs_mean),
#   SE = c(NA_SE, NAf_SE, SAs_SE)
# ) # names <- c("SHMI_1CO2", "SHMI_UNIF", "SHMI_TROP", "SHMI_POLA", "SHMI_ARCT", "SHMI_ANTA")

# # create a matrix with the means and standard errors
# data <- cbind(SHMI_mean, SHMI_SE)

# # create the barplot
# barplot(data[,1], names.arg=names, ylim=c(min(data), max(data)*1.2), 
#         col=rainbow(length(SHMI_mean)), main="SHMI Means", xlab="Groups", ylab="Means")

# # add the error bars
# arrows(x0=1:6, y0=data[,1]-data[,2], y1=data[,1]+data[,2], angle=90, code=3, length=0.1, 
#        lwd=1.5)
# colors <- c("1XCO2" = "black", "UNIFORM" = "#A6CEE3", "TROPIC" = "#FB9A99", "POLAR" = "#33A02C", "ARCTIC" = "#B2DF8A", "ANTARCTIC" = "#1F78B4")
#colors <- c("1XCO2" = "#4d4c4c", "25.76Mt" = "blue")
#colors <- c("CO2"="#4d4c4c","ARCTIC" = "#FB9A99", "UNIFORM" = "#A6CEE3", "ANTARCTIC" = "#33A02C")
colors <- c(
  "CO2"       = "red",#"#4D4D4D",   # Neutral dark gray – Baseline/control
  "UNIFORM_30MT_38hpa"   ="blue",#"#FB9A99",   # Steel blue – Well-distributed forcing
 # "UNIFORM_30MT_72hpa"   = "#A6CEE3",   # Steel blue – Well-distributed forcing"TROPIC"    = "#FDBF6F",   # Vivid green – Tropical forcing
  "UNIFORM_30MT_103hpa"   = "green",# "#B2DF8A",   # Light green – Subtropical forcing (moderate, distinct)
  "UNIFORM_35MT_103hpa"   = "#33A02C"   #"POLAR"     = "#FB8072",   # Red – Polar forcing (intense, distinct)
 # "ARCTIC"    = "#A6CEE3",   # Light blue – Arctic-specific, subtle
 # "ANTARCTIC" = "#B2DF8A"    # Light green – Antarctic-specific, mild
)
labels <- c(
  "CO2" = expression(CO[2]~"warming"),
  "UNIFORM_30MT_38hpa" = expression("SAG"["UNIF"] * "_30MT_22km"),
 # "UNIFORM_30MT_72hpa" = expression("SAG"["UNIF"] * "_30MT_18km"),
  "UNIFORM_30MT_103hpa" = expression("SAG"["UNIF"] * "_30MT_16km"),
  "UNIFORM_35MT_103hpa" = expression("SAG"["UNIF"] * "_35MT_16km")
)
# Begin plot
p <- ggplot(df_all, aes(x = Region, y = Mean, fill = Experiment)) +

  geom_bar(stat = "identity", position = position_dodge(width = 0.7), width = 0.6) +
  geom_errorbar(aes(ymin = Mean - SE, ymax = Mean + SE),
                width = 0.2, position = position_dodge(width = 0.7)) +
  scale_fill_manual(values = colors, labels = labels) +
  geom_hline(yintercept = 0, linetype = "dashed") +

  labs(x = "", y = expression(Delta~"Precipitation (%)"),
       title = "Northern Hemisphere Tropical Monsoon Regions (JJA)") +

  theme_bw() +
  theme(
    legend.title = element_blank(),
    legend.text = element_text(family = "Times New Roman", size = 20),
    legend.position = "none",
   # legend.position = c(0.7, 0.1),
    legend.direction = "horizontal",
    axis.text.x = element_text(family = "Times New Roman", size = 22, color = "black"),
    axis.text.y = element_text(family = "Times New Roman", size = 22, color = "black"),
    axis.title.y = element_text(family = "Times New Roman", size = 26, color = "black"),
    plot.title = element_text(family = "Times New Roman", size = 26, hjust = 0),
    panel.border = element_rect(colour = "black", fill = NA, size = 1.2),
    panel.grid.major = element_blank(),
    panel.grid.minor = element_blank()
  ) +
 ylim(-35, 35)

# Display plot
p

# SAsve if needed
ggsave("GroupedBar_MonsoonRegions_NH_Usha_2alt_co2.png", width = 25, height = 15, units = "cm", dpi = 300)