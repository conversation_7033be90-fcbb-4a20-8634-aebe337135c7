library(ggthemes)
library(ggplot2)
library(png)
library(ncdf4)
library('reshape2')
library(we<PERSON><PERSON>on)
library(ggrepel)
library(dplyr)

mav <- function(x,n=5){stats::filter(x,rep(1/n,n), sides=2)}
standard_error <- function(x) sd(x) / sqrt(length(x))

# Load Northern Hemisphere data
NA_1CO2 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/NA_1CO2_masked_C5.txt", header = FALSE)))
NA_2CO2 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/NA_2CO2_masked_C5.txt", header = FALSE)))
NA_38 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/NA_30MT_38hpa_masked_C5.txt", header = FALSE)))

NAf_1CO2 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/NAf_1CO2_masked_C5.txt", header = FALSE)))
NAf_2CO2 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/NAf_2CO2_masked_C5.txt", header = FALSE)))
NAf_38 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/NAf_30MT_38hpa_masked_C5.txt", header = FALSE)))

SAs_1CO2 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/SAs_1CO2_masked_C5.txt", header = FALSE)))
SAs_2CO2 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/SAs_2CO2_masked_C5.txt", header = FALSE)))
SAs_38 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/SAs_30MT_38hpa_masked_C5.txt", header = FALSE)))

# Load Southern Hemisphere data
SA_1CO2 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/SA_1CO2_masked_C5.txt", header = FALSE)))
SA_2CO2 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/SA_2CO2_masked_C5.txt", header = FALSE)))
SA_38 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/SA_30MT_38hpa_masked_C5.txt", header = FALSE)))

SAf_1CO2 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/SAf_1CO2_masked_C5.txt", header = FALSE)))
SAf_2CO2 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/SAf_2CO2_masked_C5.txt", header = FALSE)))
SAf_38 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/SAf_30MT_38hpa_masked_C5.txt", header = FALSE)))

AUS_1CO2 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/AUS_1CO2_masked_C5.txt", header = FALSE)))
AUS_2CO2 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/AUS_2CO2_masked_C5.txt", header = FALSE)))
AUS_38 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/AUS_30MT_38hpa_masked_C5.txt", header = FALSE)))

# Calculate changes

Change_NA_1CO2 = NA_2CO2-NA_1CO2
Change_NA_38 = NA_38-NA_1CO2


NA_mean=c((mean(Change_NA_1CO2/NA_1CO2)*100),
             (mean(Change_NA_38/NA_1CO2)*100))

             print(NA_mean)
NA_SE=c(standard_error(Change_NA_1CO2/NA_1CO2)*100,
                                     standard_error(Change_NA_38/NA_1CO2)*100)


Change_NAf_1CO2 = NAf_2CO2-NAf_1CO2
Change_NAf_38 = NAf_38-NAf_1CO2

NAf_mean=c((mean(Change_NAf_1CO2/NAf_1CO2)*100),
             (mean(Change_NAf_38/NAf_1CO2)*100))

             print(NAf_mean)
NAf_SE=c(standard_error(Change_NAf_1CO2/NAf_1CO2)*100,
                                     standard_error(Change_NAf_38/NAf_1CO2)*100)
                                 

# Add SAs data calculations
Change_SAs_1CO2 = SAs_2CO2-SAs_1CO2
Change_SAs_38 = SAs_38-SAs_1CO2


SAs_mean=c((mean(Change_SAs_1CO2/SAs_1CO2)*100),
             (mean(Change_SAs_38/SAs_1CO2)*100))

             print(SAs_mean)
SAs_SE=c(standard_error(Change_SAs_1CO2/SAs_1CO2)*100,
                                     standard_error(Change_SAs_38/SAs_1CO2)*100)


Change_SA_1CO2 = SA_2CO2-SA_1CO2
Change_SA_38 = SA_38-SA_1CO2


SA_mean=c((mean(Change_SA_1CO2/SA_1CO2)*100),
             (mean(Change_SA_38/SA_1CO2)*100))

           
             print(SA_mean)
SA_SE=c(standard_error(Change_SA_1CO2/SA_1CO2)*100,
                                     standard_error(Change_SA_38/SA_1CO2)*100)


Change_SAf_1CO2 = SAf_2CO2-SAf_1CO2
Change_SAf_38 = SAf_38-SAf_1CO2


SAf_mean=c((mean(Change_SAf_1CO2/SAf_1CO2)*100),
             (mean(Change_SAf_38/SAf_1CO2)*100))

           
             print(SAf_mean)
SAf_SE=c(standard_error(Change_SAf_1CO2/SAf_1CO2)*100,
                                     standard_error(Change_SAf_38/SAf_1CO2)*100)

# Add SAs data calculations
Change_AUS_1CO2 = AUS_2CO2-AUS_1CO2
Change_AUS_38 = AUS_38-AUS_1CO2


AUS_mean=c((mean(Change_AUS_1CO2/AUS_1CO2)*100),
             (mean(Change_AUS_38/AUS_1CO2)*100))

           
             print(AUS_mean)
AUS_SE=c(standard_error(Change_AUS_1CO2/AUS_1CO2)*100,
                                     standard_error(Change_AUS_38/AUS_1CO2)*100)
                                  #  standard_error(Change_AUS_72/AUS_1CO2)*100,
                                    #standard_error(Change_AUS_103/AUS_1CO2)*100,
                                    # standard_error(Change_AUS_103_35/AUS_1CO2)*100)


df_all <- data.frame(
  Region = rep(c("NA", "NAf", "SAs","SA", "SAf", "AUS"), each = 2),
  #Experiment = (c("UNIFORM", "TROPIC", "POLAR","UNIFORM", "TROPIC", "POLAR","UNIFORM", "TROPIC", "POLAR")),
    Experiment = rep(c("CO2","UNIFORM_30MT_38hpa","CO2","UNIFORM_30MT_38hpa","CO2","UNIFORM_30MT_38hpa","CO2","UNIFORM_30MT_38hpa","CO2","UNIFORM_30MT_38hpa","CO2","UNIFORM_30MT_38hpa")),

  Cntl = rep("25.76Mt", 12),
  Mean = c(NA_mean, NAf_mean, SAs_mean, SA_mean, SAf_mean, AUS_mean),
  SE = c(NA_SE, NAf_SE, SAs_SE, SA_SE, SAf_SE, AUS_SE)
)

print(df_all)
df_all$Experiment <- factor(df_all$Experiment, levels = c("CO2",  "UNIFORM_30MT_38hpa"))
df_all$Region <- factor(df_all$Region, levels = c("NA",  "NAf","SAs","SA", "SAf", "AUS"))

colors <- c(
  "CO2"       = "red",#"#4D4D4D",   # Neutral dark gray – Baseline/control
  "UNIFORM_30MT_38hpa"   ="blue")#"#FB9A99",   # Steel blue – Well-distributed forcing
  labels <- c(
  "CO2" = expression(CO[2]~"warming"),
  "UNIFORM_30MT_38hpa" = expression("SAG"["UNIF"] * "_30MT_22km")

)

# Create histogram plot
p <- ggplot(df_all, aes(x = Region, y = Mean, fill = Experiment)) +

  geom_bar(stat = "identity", position = position_dodge(width = 0.7), width = 0.6) +
  geom_errorbar(aes(ymin = Mean - SE, ymax = Mean + SE),
                position = position_dodge(width = 0.7), width = 0.3) +
  scale_fill_manual(values = colors, labels = labels) +
  geom_hline(yintercept = 0, linetype = "dashed") +
  labs(x = "", y = expression(Delta~"Precipitation (%)"),
       title = "Summer monsoon precipitation") +
  theme_bw() +
  theme(
    legend.title = element_blank(),
    legend.text = element_text(family = "Times New Roman", size = 20),
    #legend.position = "none",
   legend.position = c(0.7, 0.1),
    legend.direction = "horizontal",
    axis.text.x = element_text(family = "Times New Roman", size = 22, color = "black"),
    axis.text.y = element_text(family = "Times New Roman", size = 22, color = "black"),
    axis.title.y = element_text(family = "Times New Roman", size = 26, color = "black"),
    plot.title = element_text(family = "Times New Roman", size = 26, hjust = 0),
    panel.border = element_rect(colour = "black", fill = NA, size = 1.2),
    panel.grid.major = element_blank(),
    panel.grid.minor = element_blank()
  ) +
 ylim(-35, 35)

# Display plot
p

ggsave("Regional_Monsoon_Histogram_CO2_38hpa.png", width = 30, height = 20, units = "cm", dpi = 300)