library(ggthemes)
# setwd("/Volumes/CAOS_2/Analysis/292.7/Figures/Sensitivity_Figures/ISMR_ERF_TG/CleanFigure")
library(ggplot2) # package for plotting
library(png)
library(ncdf4)
library('reshape2')
library(<PERSON><PERSON><PERSON><PERSON>)
library(ggrepel)
# library(ggpubr)
library(dplyr)
mav <- function(x,n=5){stats::filter(x,rep(1/n,n), sides=2)} #moving average fuction
standard_error <- function(x) sd(x) / sqrt(length(x)) # Create own function  (https://statisticsglobe.com/standard-error-in-r-example)


SA_1CO2 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/SA_1CO2_masked_C5.txt", header = FALSE)))
SA_2CO2 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/SA_2CO2_masked_C5.txt", header = FALSE))) #for just three points

SA_22_5_38 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/SA_30MT_38hpa_masked_C5.txt", header = FALSE)))
SA_22_5_72 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/SA_30MT_72hpa_masked_C5.txt", header = FALSE)))
SA_22_5_103 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/SA_30MT_103hpa_masked_C5.txt", header = FALSE)))
SA_22_5_103_35 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/SA_35MT_103hpa_masked_C5.txt", header = FALSE)))


SAf_1CO2 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/SAf_1CO2_masked_C5.txt", header = FALSE)))
SAf_2CO2 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/SAf_2CO2_masked_C5.txt", header = FALSE))) #for just three points

SAf_22_5_38 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/SAf_30MT_38hpa_masked_C5.txt", header = FALSE)))
SAf_22_5_72 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/SAf_30MT_72hpa_masked_C5.txt", header = FALSE)))
SAf_22_5_103 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/SAf_30MT_103hpa_masked_C5.txt", header = FALSE)))
SAf_22_5_103_35 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/SAf_35MT_103hpa_masked_C5.txt", header = FALSE)))


AUS_1CO2 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/AUS_1CO2_masked_C5.txt", header = FALSE)))
AUS_2CO2 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/AUS_2CO2_masked_C5.txt", header = FALSE))) #for just three points

AUS_22_5_38 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/AUS_30MT_38hpa_masked_C5.txt", header = FALSE)))
AUS_22_5_72 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/AUS_30MT_72hpa_masked_C5.txt", header = FALSE)))
AUS_22_5_103 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/AUS_30MT_103hpa_masked_C5.txt", header = FALSE)))
AUS_22_5_103_35 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/AUS_35MT_103hpa_masked_C5.txt", header = FALSE)))

# ###########################
# # ###########################
Change_SA_1CO2 = SA_2CO2-SA_1CO2
Change_SA_38 = SA_22_5_38-SA_1CO2
Change_SA_72 = SA_22_5_72-SA_1CO2
Change_SA_103 = SA_22_5_103-SA_1CO2
Change_SA_103_35 = SA_22_5_103_35-SA_1CO2

SA_mean=c((mean(Change_SA_1CO2/SA_1CO2)*100),
             (mean(Change_SA_38/SA_1CO2)*100))
      #       (mean(Change_SA_72/SA_1CO2)*100),
        #   (mean(Change_SA_103/SA_1CO2)*100),
        #     (mean(Change_SA_103_35/SA_1CO2)*100))
           
             print(SA_mean)
SA_SE=c(standard_error(Change_SA_1CO2/SA_1CO2)*100,
                                     standard_error(Change_SA_38/SA_1CO2)*100)
                               #     standard_error(Change_SA_72/SA_1CO2)*100,
                                   # standard_error(Change_SA_103/SA_1CO2)*100,
                                   #  standard_error(Change_SA_103_35/SA_1CO2)*100)

Change_SAf_1CO2 = SAf_2CO2-SAf_1CO2
Change_SAf_38 = SAf_22_5_38-SAf_1CO2
Change_SAf_72 = SAf_22_5_72-SAf_1CO2
Change_SAf_103 = SAf_22_5_103-SAf_1CO2
Change_SAf_103_35 = SAf_22_5_103_35-SAf_1CO2

SAf_mean=c((mean(Change_SAf_1CO2/SAf_1CO2)*100),
             (mean(Change_SAf_38/SAf_1CO2)*100))
            # (mean(Change_SAf_72/SAf_1CO2)*100),
        #   (mean(Change_SAf_103/SAf_1CO2)*100),
         #    (mean(Change_SAf_103_35/SAf_1CO2)*100))
           
             print(SAf_mean)
SAf_SE=c(standard_error(Change_SAf_1CO2/SAf_1CO2)*100,
                                     standard_error(Change_SAf_38/SAf_1CO2)*100)
                                 #   standard_error(Change_SAf_72/SAf_1CO2)*100,
                                  #  standard_error(Change_SAf_103/SAf_1CO2)*100,
                                   #  standard_error(Change_SAf_103_35/SAf_1CO2)*100)

# Add SAs data calculations
Change_AUS_1CO2 = AUS_2CO2-AUS_1CO2
Change_AUS_38 = AUS_22_5_38-AUS_1CO2
Change_AUS_72 = AUS_22_5_72-AUS_1CO2
Change_AUS_103 = AUS_22_5_103-AUS_1CO2
Change_AUS_103_35 = AUS_22_5_103_35-AUS_1CO2

AUS_mean=c((mean(Change_AUS_1CO2/AUS_1CO2)*100),
             (mean(Change_AUS_38/AUS_1CO2)*100))
     #        (mean(Change_AUS_72/AUS_1CO2)*100),
      #     (mean(Change_AUS_103/AUS_1CO2)*100),
      #       (mean(Change_AUS_103_35/AUS_1CO2)*100))
           
             print(AUS_mean)
AUS_SE=c(standard_error(Change_AUS_1CO2/AUS_1CO2)*100,
                                     standard_error(Change_AUS_38/AUS_1CO2)*100)
                                  #  standard_error(Change_AUS_72/AUS_1CO2)*100,
                                    #standard_error(Change_AUS_103/AUS_1CO2)*100,
                                    # standard_error(Change_AUS_103_35/AUS_1CO2)*100)

# Create histogram data

#hist_data <- data.frame(
 # Region = rep(c("NA", "NAf", "SAs"), each = 5),
  #Experiment = rep(c("UNIFORM", "TROPIC", "POLAR"), 3),
 # Experiment = rep(c("CO2", "UNIFORM_30MT_38hpa", "UNIFORM_30MT_72hpa", "UNIFORM_30MT_103hpa", "UNIFORM_35MT_103hpa","CO2", "UNIFORM_30MT_38hpa", "UNIFORM_30MT_72hpa", "UNIFORM_30MT_103hpa", "UNIFORM_35MT_103hpa","CO2", "UNIFORM_30MT_38hpa", "UNIFORM_30MT_72hpa", "UNIFORM_30MT_103hpa", "UNIFORM_35MT_103hpa")),

  #Value = c((Change_SA_1CO2/SA_1CO2)*100,
    #(Change_SA_38/SA_1CO2)*100,
    #(Change_SA_72/SA_1CO2)*100,
   # (Change_SA_103/SA_1CO2)*100,
   # (Change_SA_103_35/SA_1CO2)*100,
   # (Change_SAf_1CO2/SAf_1CO2)*100,
   # (Change_SAf_38/SAf_1CO2)*100,
    #(Change_SAf_72/SAf_1CO2)*100,
    #(Change_SAf_103/SAf_1CO2)*100,
    #(Change_SAf_103_35/SAf_1CO2)*100,
   # (Change_AUS_1CO2/AUS_1CO2)*100,
   # (Change_AUS_38/AUS_1CO2)*100,
   # (Change_AUS_72/AUS_1CO2)*100,
   # (Change_AUS_103/AUS_1CO2)*100
   # (Change_AUS_103_35/AUS_1CO2)*100
 # )
#)
# Create histogram plot
#hist_colors <- c("UNIF" = "#A6CEE3", "TROP" = "#FB9A99", "POLA" = "#33A02C")

#p_hist <- ggplot(hist_data, aes(x = Value, fill = Experiment)) +
  #geom_histogram(alpha = 0.7, position = "identity", bins = 20) +
  #facet_grid(Region ~ Experiment, scales = "free") +
 # scale_fill_manual(values = hist_colors) +
 # labs(x = "Δ Precipitation (%)", y = "Frequency", 
 #      title = "Distribution of Precipitation Changes by Region and Experiment") +
 # theme_bw() +
 # theme(
  #  strip.text = element_text(family = "Times New Roman", size = 12),
  #  axis.text = element_text(family = "Times New Roman", size = 10),
 # axis.title = element_text(family = "Times New Roman", size = 14),
  #  plot.title = element_text(family = "Times New Roman", size = 16, hjust = 0.5),
  #  legend.position = "none"
 #) +
 #geom_vline(xintercept = 0, linetype = "dashed", alpha = 0.5)

#p_hist

#ggSAsve("Monsoon_regions_histogram_UNIF_TROP_POLA.png", width = 30, height = 20, units = "cm", dpi = 300)

df_all <- data.frame(
  Region = rep(c("South America", "South Africa", "Australia"), each = 2),
  #Experiment = (c("UNIFORM", "TROPIC", "POLAR","UNIFORM", "TROPIC", "POLAR","UNIFORM", "TROPIC", "POLAR")),
    Experiment = (c("CO2","UNIFORM_30MT_38hpa","CO2","UNIFORM_30MT_38hpa", "CO2","UNIFORM_30MT_38hpa")),

  Cntl = rep("25.76Mt", 6),
  Mean = c(SA_mean, SAf_mean, AUS_mean),
  SE = c(SA_SE, SAf_SE, AUS_SE)
)
print(df_all)
#df_all$Experiment <- factor(df_all$Experiment, levels = c("CO2", "UNIFORM", "ARCTIC", "ANTARCTIC"))
df_all$Experiment <- factor(df_all$Experiment, levels = c("CO2",  "UNIFORM_30MT_38hpa"))

# Alternative approach if you want to keep 1XCO2 in the data frame
# df_all <- data.frame(
#   Region = c(rep("NA", 3), rep("NAf", 3), rep("SAs", 3)),
#   Experiment = rep(c("UNIFORM", "TROPIC", "POLAR"), 3),
#   Cntl = rep("25.76Mt", 9),
#   Mean = c(SA_mean, SAf_mean, AUS_mean),
#   SE = c(SA_SE, SAf_SE, AUS_SE)
# ) # names <- c("SHMI_1CO2", "SHMI_UNIF", "SHMI_TROP", "SHMI_POLA", "SHMI_ARCT", "SHMI_ANTA")

# # create a matrix with the means and standard errors
# data <- cbind(SHMI_mean, SHMI_SE)

# # create the barplot
# barplot(data[,1], names.arg=names, ylim=c(min(data), max(data)*1.2), 
#         col=rainbow(length(SHMI_mean)), main="SHMI Means", xlab="Groups", ylab="Means")

# # add the error bars
# arrows(x0=1:6, y0=data[,1]-data[,2], y1=data[,1]+data[,2], angle=90, code=3, length=0.1, 
#        lwd=1.5)
# colors <- c("1XCO2" = "black", "UNIFORM" = "#A6CEE3", "TROPIC" = "#FB9A99", "POLAR" = "#33A02C", "ARCTIC" = "#B2DF8A", "ANTARCTIC" = "#1F78B4")
#colors <- c("1XCO2" = "#4d4c4c", "25.76Mt" = "blue")
#colors <- c("CO2"="#4d4c4c","ARCTIC" = "#FB9A99", "UNIFORM" = "#A6CEE3", "ANTARCTIC" = "#33A02C")
colors <- c(
  "CO2"       = "red",#"#4D4D4D",   # Neutral dark gray – Baseline/control
  "UNIFORM_30MT_38hpa"   ="blue"#"#FB9A99",   # Steel blue – Well-distributed forcing
 # "UNIFORM_30MT_72hpa"   = "#A6CEE3",   # Steel blue – Well-distributed forcing"TROPIC"    = "#FDBF6F",   # Vivid green – Tropical forcing
 # "UNIFORM_30MT_103hpa"   = "green",# "#B2DF8A",   # Light green – Subtropical forcing (moderate, distinct)
 # "UNIFORM_35MT_103hpa"   = "#33A02C"   #"POLAR"     = "#FB8072",   # Red – Polar forcing (intense, distinct)
 # "ARCTIC"    = "#A6CEE3",   # Light blue – Arctic-specific, subtle
 # "ANTARCTIC" = "#B2DF8A"    # Light green – Antarctic-specific, mild
)
labels <- c(
  "CO2" = expression(CO[2]~"warming"),
  "UNIFORM_30MT_38hpa" = expression("SAG"["UNIF"] * "_30MT_22km")
 # "UNIFORM_30MT_72hpa" = expression("SAG"["UNIF"] * "_30MT_18km"),
 # "UNIFORM_30MT_103hpa" = expression("SAG"["UNIF"] * "_30MT_16km"),
  #"UNIFORM_35MT_103hpa" = expression("SAG"["UNIF"] * "_35MT_16km")
)
# Begin plot
p <- ggplot(df_all, aes(x = Region, y = Mean, fill = Experiment)) +

  geom_bar(stat = "identity", position = position_dodge(width = 0.4), width = 0.4) +
  geom_errorbar(aes(ymin = Mean - SE, ymax = Mean + SE),
                width = 0.2, position = position_dodge(width = 0.4)) +
  scale_fill_manual(values = colors, labels = labels) +
  geom_hline(yintercept = 0, linetype = "dashed") +

  labs(x = "", y = expression(Delta~"Precipitation (%)"),
       title = "Southern Hemisphere Tropical Monsoon Regions (DJF)") +

  theme_bw() +
  theme(
    legend.title = element_blank(),
    legend.text = element_text(family = "Times New Roman", size = 20),
    #legend.position = "none",
   legend.position = c(0.82, 0.1),
    legend.direction = "vertical",
    axis.text.x = element_text(family = "Times New Roman", size = 22, color = "black"),
    axis.text.y = element_text(family = "Times New Roman", size = 22, color = "black"),
    axis.title.y = element_text(family = "Times New Roman", size = 26, color = "black"),
    plot.title = element_text(family = "Times New Roman", size = 26, hjust = 0),
    panel.border = element_rect(colour = "black", fill = NA, size = 1.2),
    panel.grid.major = element_blank(),
    panel.grid.minor = element_blank()
  ) +
 ylim(-35, 35)

# Display plot
p

# SAsve if needed
ggsave("GroupedBar_MonsoonRegions_SH_Usha_unif.png", width = 25, height = 15, units = "cm", dpi = 300)