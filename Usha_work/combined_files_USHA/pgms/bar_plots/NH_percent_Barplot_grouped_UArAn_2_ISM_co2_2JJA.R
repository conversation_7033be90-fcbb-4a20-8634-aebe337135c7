library(ggthemes)
# setwd("/Volumes/CAOS_2/Analysis/292.7/Figures/Sensitivity_Figures/ISMR_ERF_TG/CleanFigure")
library(ggplot2) # package for plotting
library(png)
library(ncdf4)
library('reshape2')
library(we<PERSON><PERSON><PERSON>)
library(ggrepel)
# library(ggpubr)
library(dplyr)
mav <- function(x,n=5){stats::filter(x,rep(1/n,n), sides=2)} #moving average fuction
standard_error <- function(x) sd(x) / sqrt(length(x)) # Create own function  (https://statisticsglobe.com/standard-error-in-r-example)


NA_1CO2 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/barplots_histograms/monsoon_regions/data/NHMI/NA_1CO2_masked_new_2lev.txt", header = FALSE)))
NA_2CO2 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/barplots_histograms/monsoon_regions/data/NHMI/NA_2CO2_masked_new_2lev.txt", header = FALSE))) #for just three points

NA_22_5_UNIF <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/barplots_histograms/monsoon_regions/data/NHMI/NA_UNIF_22_5_masked_new_2lev.txt", header = FALSE)))
NA_22_5_TROP <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/barplots_histograms/monsoon_regions/data/NHMI/NA_TROP_22_5_masked_new_2lev.txt", header = FALSE)))
NA_22_5_POLA <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/barplots_histograms/monsoon_regions/data/NHMI/NA_POLA_22_5_masked_new_2lev.txt", header = FALSE)))
NA_22_5_ARCT <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/barplots_histograms/monsoon_regions/data/NHMI/NA_ARCT_22_5_masked_new_2lev.txt", header = FALSE)))
NA_22_5_ANTA <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/barplots_histograms/monsoon_regions/data/NHMI/NA_ANTA_22_5_masked_new_2lev.txt", header = FALSE)))

NAf_1CO2 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/barplots_histograms/monsoon_regions/data/NHMI/NAf_1CO2_masked_new_2lev.txt", header = FALSE)))
NAf_2CO2 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/barplots_histograms/monsoon_regions/data/NHMI/NAf_2CO2_masked_new_2lev.txt", header = FALSE))) #for just three points

NAf_22_5_UNIF <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/barplots_histograms/monsoon_regions/data/NHMI/NAf_UNIF_22_5_masked_new_2lev.txt", header = FALSE)))
NAf_22_5_TROP <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/barplots_histograms/monsoon_regions/data/NHMI/NAf_TROP_22_5_masked_new_2lev.txt", header = FALSE)))
NAf_22_5_POLA <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/barplots_histograms/monsoon_regions/data/NHMI/NAf_POLA_22_5_masked_new_2lev.txt", header = FALSE)))
NAf_22_5_ARCT <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/barplots_histograms/monsoon_regions/data/NHMI/NAf_ARCT_22_5_masked_new_2lev.txt", header = FALSE)))
NAf_22_5_ANTA <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/barplots_histograms/monsoon_regions/data/NHMI/NAf_ANTA_22_5_masked_new_2lev.txt", header = FALSE)))

IND_1CO2 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/ITCZ/JJA/IND/India_mask_P_mean_1CO2_JJA.txt", header = FALSE)))
IND_2CO2 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/ITCZ/JJA/IND/India_mask_P_mean_2CO2_JJA.txt", header = FALSE))) #for just three points

IND_22_5_UNIF <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/ITCZ/JJA/IND/India_mask_P_mean_UNIF_JJA.txt", header = FALSE)))
IND_22_5_TROP <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/ITCZ/JJA/IND/India_mask_P_mean_TROP_JJA.txt", header = FALSE)))
IND_22_5_POLA <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/ITCZ/JJA/IND/India_mask_P_mean_POLA_JJA.txt", header = FALSE)))
IND_22_5_ARCT <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/ITCZ/JJA/IND/India_mask_P_mean_ARCT_JJA.txt", header = FALSE)))
IND_22_5_ANTA <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/ITCZ/JJA/IND/India_mask_P_mean_ANTA_JJA.txt", header = FALSE)))

SAs_1CO2 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/barplots_histograms/monsoon_regions/data/NHMI/SAs_1CO2_masked_new_2lev.txt", header = FALSE)))
SAs_2CO2 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/barplots_histograms/monsoon_regions/data/NHMI/SAs_2CO2_masked_new_2lev.txt", header = FALSE))) #for just three points

SAs_22_5_UNIF <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/barplots_histograms/monsoon_regions/data/NHMI/SAs_UNIF_22_5_masked_new_2lev.txt", header = FALSE)))
SAs_22_5_TROP <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/barplots_histograms/monsoon_regions/data/NHMI/SAs_TROP_22_5_masked_new_2lev.txt", header = FALSE)))
SAs_22_5_POLA <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/barplots_histograms/monsoon_regions/data/NHMI/SAs_POLA_22_5_masked_new_2lev.txt", header = FALSE)))
SAs_22_5_ARCT <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/barplots_histograms/monsoon_regions/data/NHMI/SAs_ARCT_22_5_masked_new_2lev.txt", header = FALSE)))
SAs_22_5_ANTA <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/barplots_histograms/monsoon_regions/data/NHMI/SAs_ANTA_22_5_masked_new_2lev.txt", header = FALSE)))

# ###########################
# # ###########################
Change_NA_1CO2 = NA_2CO2-NA_1CO2
Change_NA_UNIF_22_5 = NA_22_5_UNIF-NA_1CO2
Change_NA_TROP_22_5 = NA_22_5_TROP-NA_1CO2
Change_NA_POLA_22_5 = NA_22_5_POLA-NA_1CO2
Change_NA_ARCT_22_5 = NA_22_5_ARCT-NA_1CO2
Change_NA_ANTA_22_5 = NA_22_5_ANTA-NA_1CO2

NA_mean=c((mean(Change_NA_1CO2/NA_1CO2)*100),
             (mean(Change_NA_UNIF_22_5/NA_1CO2)*100),
            #(mean(Change_NA_TROP_22_5/NA_1CO2)*100),
            # (mean(Change_NA_POLA_22_5/NA_1CO2)*100),
           (mean(Change_NA_ARCT_22_5/NA_1CO2)*100),
             (mean(Change_NA_ANTA_22_5/NA_1CO2)*100))
           
             print(NA_mean)
NA_SE=c(standard_error(Change_NA_1CO2/NA_1CO2)*100,
                                     standard_error(Change_NA_UNIF_22_5/NA_1CO2)*100,
                              #       standard_error(Change_NA_TROP_22_5/NA_1CO2)*100,
                              #      standard_error(Change_NA_POLA_22_5/NA_1CO2)*100,
                                    standard_error(Change_NA_ARCT_22_5/NA_1CO2)*100,
                                     standard_error(Change_NA_ANTA_22_5/NA_1CO2)*100)
Change_NAf_1CO2 = NAf_2CO2 - NAf_1CO2
Change_NAf_UNIF = NAf_22_5_UNIF - NAf_1CO2
Change_NAf_TROP = NAf_22_5_TROP - NAf_1CO2
Change_NAf_POLA = NAf_22_5_POLA - NAf_1CO2
Change_NAf_ARCT = NAf_22_5_ARCT - NAf_1CO2
Change_NAf_ANTA = NAf_22_5_ANTA - NAf_1CO2

NAf_mean <- c((mean(Change_NAf_1CO2 / NAf_1CO2) * 100),
              (mean(Change_NAf_UNIF / NAf_1CO2) * 100),
             # (mean(Change_NAf_TROP / NAf_1CO2) * 100),
             #(mean(Change_NAf_POLA / NAf_1CO2) * 100),
                (mean(Change_NAf_ARCT / NAf_1CO2) * 100),
                (mean(Change_NAf_ANTA / NAf_1CO2) * 100))

NAf_SE <- c(standard_error(Change_NAf_1CO2 / NAf_1CO2) * 100,
            standard_error(Change_NAf_UNIF / NAf_1CO2) * 100,
           #standard_error(Change_NAf_TROP / NAf_1CO2) * 100,
           #standard_error(Change_NAf_POLA / NAf_1CO2) * 100,
              standard_error(Change_NAf_ARCT / NAf_1CO2) * 100,
               standard_error(Change_NAf_ANTA / NAf_1CO2) * 100)

Change_IND_1CO2 = IND_2CO2 - IND_1CO2
Change_IND_UNIF = IND_22_5_UNIF - IND_1CO2
Change_IND_TROP = IND_22_5_TROP - IND_1CO2
Change_IND_POLA = IND_22_5_POLA - IND_1CO2
Change_IND_ARCT = IND_22_5_ARCT - IND_1CO2
Change_IND_ANTA = IND_22_5_ANTA - IND_1CO2

IND_mean <- c((mean(Change_IND_1CO2 / IND_1CO2) * 100),
             (mean(Change_IND_UNIF / IND_1CO2) * 100),
            #(mean(Change_IND_TROP / IND_1CO2) * 100),
            #(mean(Change_IND_POLA / IND_1CO2) * 100),
              (mean(Change_IND_ARCT / IND_1CO2) * 100),
              (mean(Change_IND_ANTA / IND_1CO2) * 100))

IND_SE <- c(standard_error(Change_IND_1CO2 / IND_1CO2) * 100,
           standard_error(Change_IND_UNIF / IND_1CO2) * 100,
         #standard_error(Change_IND_TROP / IND_1CO2) * 100,
         #standard_error(Change_IND_POLA / IND_1CO2) * 100,
            standard_error(Change_IND_ARCT / IND_1CO2) * 100,
            standard_error(Change_IND_ANTA / IND_1CO2) * 100)
# Add SAs data calculations
Change_SAs_1CO2 = SAs_2CO2 - SAs_1CO2
Change_SAs_UNIF = SAs_22_5_UNIF - SAs_1CO2
Change_SAs_TROP = SAs_22_5_TROP - SAs_1CO2
Change_SAs_POLA = SAs_22_5_POLA - SAs_1CO2
Change_SAs_ARCT = SAs_22_5_ARCT - SAs_1CO2
Change_SAs_ANTA = SAs_22_5_ANTA - SAs_1CO2

SAs_mean <- c((mean(Change_SAs_1CO2 / SAs_1CO2) * 100),
             (mean(Change_SAs_UNIF / SAs_1CO2) * 100),
           # (mean(Change_SAs_TROP / SAs_1CO2) * 100),
            #(mean(Change_SAs_POLA / SAs_1CO2) * 100),
              (mean(Change_SAs_ARCT / SAs_1CO2) * 100),
              (mean(Change_SAs_ANTA / SAs_1CO2) * 100))

SAs_SE <- c(standard_error(Change_SAs_1CO2 / SAs_1CO2) * 100,
           standard_error(Change_SAs_UNIF / SAs_1CO2) * 100,
         #standard_error(Change_SAs_TROP / SAs_1CO2) * 100,
         #standard_error(Change_SAs_POLA / SAs_1CO2) * 100,
            standard_error(Change_SAs_ARCT / SAs_1CO2) * 100,
            standard_error(Change_SAs_ANTA / SAs_1CO2) * 100)

# Create histogram data

#hist_data <- data.frame(
#  Region = rep(c("NA", "NAf", "SAs"), each = 4),
  #Experiment = rep(c("UNIFORM", "TROPIC", "POLAR"), 3),
 #   Experiment = rep(c("CO2", "UNIFORM", "TROPIC", "POLAR", "ARCTIC", "ANTARCTIC"), 3),

  #Value = c((Change_NA_1CO2/NA_1CO2)*100,
   # (Change_NA_UNIF_22_5/NA_1CO2)*100,
    #(Change_NA_TROP_22_5/NA_1CO2)*100,
    #(Change_NA_POLA_22_5/NA_1CO2)*100,
    #(Change_NA_ARCT_22_5/NA_1CO2)*100,
    #(Change_NA_ANTA_22_5/NA_1CO2)*100,
    #(Change_NAf_1CO2/NAf_1CO2)*100,
    #(Change_NAf_UNIF/NAf_1CO2)*100,
    #(Change_NAf_TROP/NAf_1CO2)*100,
    #(Change_NAf_POLA/NAf_1CO2)*100,
    #(Change_NAf_ARCT/NAf_1CO2)*100,
    #(Change_NAf_ANTA/NAf_1CO2)*100,
    #(Change_SAs_1CO2/SAs_1CO2)*100,
    #(Change_SAs_UNIF/SAs_1CO2)*100,
    #(Change_SAs_TROP/SAs_1CO2)*100,
    #(Change_SAs_POLA/SAs_1CO2)*100
    #(Change_SAs_ARCT/SAs_1CO2)*100,
    #(Change_SAs_ANTA/SAs_1CO2)*100
  #)
#)
# Create histogram plot
#hist_colors <- c("UNIF" = "#A6CEE3", "TROP" = "#FB9A99", "POLA" = "#33A02C")

#p_hist <- ggplot(hist_data, aes(x = Value, fill = Experiment)) +
  #geom_histogram(alpha = 0.7, position = "identity", bins = 20) +
  #facet_grid(Region ~ Experiment, scales = "free") +
 # scale_fill_manual(values = hist_colors) +
 # labs(x = "Δ Precipitation (%)", y = "Frequency", 
 #      title = "Distribution of Precipitation Changes by Region and Experiment") +
 # theme_bw() +
 # theme(
  #  strip.text = element_text(family = "Times New Roman", size = 12),
  #  axis.text = element_text(family = "Times New Roman", size = 10),
 # axis.title = element_text(family = "Times New Roman", size = 14),
  #  plot.title = element_text(family = "Times New Roman", size = 16, hjust = 0.5),
  #  legend.position = "none"
 #) +
 #geom_vline(xintercept = 0, linetype = "dashed", alpha = 0.5)

#p_hist

#ggSAsve("Monsoon_regions_histogram_UNIF_TROP_POLA.png", width = 30, height = 20, units = "cm", dpi = 300)

df_all <- data.frame(
  Region = rep(c("North America", "North Africa", "South Asian","India"), each = 4),
  #Experiment = (c("UNIFORM", "TROPIC", "POLAR","UNIFORM", "TROPIC", "POLAR","UNIFORM", "TROPIC", "POLAR")),
   #Experiment = (c("CO2","UNIFORM","TROPIC", "POLAR", "ARCTIC", "ANTARCTIC","CO2","UNIFORM", "TROPIC", "POLAR","ARCTIC", "ANTARCTIC","CO2","UNIFORM", "TROPIC", "POLAR","ARCTIC", "ANTARCTIC","CO2","UNIFORM", "TROPIC", "POLAR","ARCTIC", "ANTARCTIC")),
    Experiment = (c("CO2","UNIFORM", "ARCTIC", "ANTARCTIC","CO2","UNIFORM","ARCTIC", "ANTARCTIC","CO2","UNIFORM", "ARCTIC", "ANTARCTIC","CO2","UNIFORM", "ARCTIC", "ANTARCTIC")),

  Cntl = rep("25.76Mt", 16),
  Mean = c(NA_mean, NAf_mean, SAs_mean, IND_mean),
  SE = c(NA_SE, NAf_SE, SAs_SE, IND_SE)
)
print(df_all)
df_all$Experiment <- factor(df_all$Experiment, levels = c("CO2", "UNIFORM","TROPIC", "POLAR", "ARCTIC", "ANTARCTIC"))
df_all$Region <- factor(df_all$Region, levels = c("North Africa", "North America", "South Asian", "India"))
df_all$Experiment <- factor(df_all$Experiment, levels = c("CO2",  "ARCTIC", "UNIFORM","ANTARCTIC"))

# Alternative approach if you want to keep 1XCO2 in the data frame
# df_all <- data.frame(
#   Region = c(rep("NA", 3), rep("NAf", 3), rep("SAs", 3)),
#   Experiment = rep(c("UNIFORM", "TROPIC", "POLAR"), 3),
#   Cntl = rep("25.76Mt", 9),
#   Mean = c(NA_mean, NAf_mean, SAs_mean),
#   SE = c(NA_SE, NAf_SE, SAs_SE)
# ) # names <- c("SHMI_1CO2", "SHMI_UNIF", "SHMI_TROP", "SHMI_POLA", "SHMI_ARCT", "SHMI_ANTA")

# # create a matrix with the means and standard errors
# data <- cbind(SHMI_mean, SHMI_SE)

# # create the barplot
# barplot(data[,1], names.arg=names, ylim=c(min(data), max(data)*1.2), 
#         col=rainbow(length(SHMI_mean)), main="SHMI Means", xlab="Groups", ylab="Means")

# # add the error bars
# arrows(x0=1:6, y0=data[,1]-data[,2], y1=data[,1]+data[,2], angle=90, code=3, length=0.1, 
#        lwd=1.5)
# colors <- c("1XCO2" = "black", "UNIFORM" = "#A6CEE3", "TROPIC" = "#FB9A99", "POLAR" = "#33A02C", "ARCTIC" = "#B2DF8A", "ANTARCTIC" = "#1F78B4")
#colors <- c("1XCO2" = "#4d4c4c", "25.76Mt" = "blue")
#colors <- c("CO2"="#4d4c4c","ARCTIC" = "#FB9A99", "UNIFORM" = "#A6CEE3", "ANTARCTIC" = "#33A02C", "TROPIC" = "#B2DF8A", "POLAR" = "#1F78B4")
colors <- c(
  "CO2"       = "#4D4D4D",   # Neutral dark gray – Baseline/control
  "UNIFORM"   =  "#FB9A99",   # Steel blue – Well-distributed forcing
  "TROPIC"    = "#FDBF6F",   # Vivid green – Tropical forcing
  "POLAR"     = "slateblue",   # Red – Polar forcing (intense, distinct)
"ARCTIC"    = "#A6CEE3",   # Light blue – Arctic-specific, subtle
  "ANTARCTIC" = "#B2DF8A"    # Light green – Antarctic-specific, mild
)
labels <- c(
  "CO2" = expression(CO[2]),
  "UNIFORM" = "Uniform",
  "TROPIC" = "Tropic",
  "POLAR" = "Polar",
  "ARCTIC" = "Arctic",
  "ANTARCTIC" = "Antarctic"
)
# Begin plot
p <- ggplot(df_all, aes(x = Region, y = Mean, fill = Experiment)) +

  geom_bar(stat = "identity", position = position_dodge(width = 0.7), width = 0.6) +
  geom_errorbar(aes(ymin = Mean - SE, ymax = Mean + SE),
                width = 0.2, position = position_dodge(width = 0.7)) +
  scale_fill_manual(values = colors, labels = labels) +
  geom_hline(yintercept = 0, linetype = "dashed") +

  labs(x = "", y = expression(Delta~"Precipitation (%)"),
       title = "Northern Hemisphere Tropical Monsoon Regions (JJA)") +
  guides(fill = guide_legend(nrow = 1)) +  # ← this forces one-row legend

  theme_bw() +
  theme(
    legend.title = element_blank(),
    legend.text = element_text(family = "Times New Roman", size = 20),
    legend.position = "none",
    #legend.position = c(0.6, 0.1),
    legend.direction = "horizontal",
    axis.text.x = element_text(family = "Times New Roman", size = 22, color = "black"),
    axis.text.y = element_text(family = "Times New Roman", size = 22, color = "black"),
    axis.title.y = element_text(family = "Times New Roman", size = 26, color = "black"),
    plot.title = element_text(family = "Times New Roman", size = 26, hjust = 0),
    panel.border = element_rect(colour = "black", fill = NA, size = 1.2),
    panel.grid.major = element_blank(),
    panel.grid.minor = element_blank()
  ) +
 ylim(-40, 40)

# Display plot
p

# SAsve if needed
ggsave("GroupedBar_MonsoonRegions_NH_UArAn_ISMI_co2_JJA.png", width = 25, height = 15, units = "cm", dpi = 300)