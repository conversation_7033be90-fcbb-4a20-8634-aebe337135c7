library(ggthemes)
# setwd("/Volumes/CAOS_2/Analysis/292.7/Figures/Sensitivity_Figures/ISMR_ERF_TG/CleanFigure")
library(ggplot2) # package for plotting
library(png)
library(ncdf4)
library('reshape2')
library(we<PERSON><PERSON><PERSON>)
library(ggrepel)
# library(ggpubr)
library(dplyr)
mav <- function(x,n=5){stats::filter(x,rep(1/n,n), sides=2)} #moving average fuction
standard_error <- function(x) sd(x) / sqrt(length(x)) # Create own function  (https://statisticsglobe.com/standard-error-in-r-example)


SA_1CO2 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/barplots_histograms/monsoon_regions/data/SHMI/SA_1CO2_masked_new.txt", header = FALSE)))
SA_2CO2 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/barplots_histograms/monsoon_regions/data/SHMI/SA_2CO2_masked_new.txt", header = FALSE))) #for just three points

SA_22_5_UNIF <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/barplots_histograms/monsoon_regions/data/SHMI/SA_UNIF_masked_22_5_new_2lev.txt", header = FALSE)))
#SA_22_5_TROP <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/barplots_histograms/monsoon_regions/data/SHMI/SA_TROP_masked_22_5_new_2lev.txt", header = FALSE)))
#SA_22_5_POLA <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/barplots_histograms/monsoon_regions/data/SHMI/SA_POLA_masked_22_5_new_2lev.txt", header = FALSE)))
SA_22_5_ARCT <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/barplots_histograms/monsoon_regions/data/SHMI/SA_ARCT_masked_22_5_new_2lev.txt", header = FALSE)))
SA_22_5_ANTA <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/barplots_histograms/monsoon_regions/data/SHMI/SA_ANTA_masked_22_5_new_2lev.txt", header = FALSE)))

SAf_1CO2 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/barplots_histograms/monsoon_regions/data/SHMI/SAf_1CO2_masked_new.txt", header = FALSE)))
SAf_2CO2 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/barplots_histograms/monsoon_regions/data/SHMI/SAf_2CO2_masked_new.txt", header = FALSE))) #for just three points

SAf_22_5_UNIF <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/barplots_histograms/monsoon_regions/data/SHMI/SAf_UNIF_masked_22_5_new_2lev.txt", header = FALSE)))
#NAf_22_5_TROP <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/barplots_histograms/monsoon_regions/data/SHMI/NAf_TROP_masked_22_5_new_2lev.txt", header = FALSE)))
#NAf_22_5_POLA <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/barplots_histograms/monsoon_regions/data/SHMI/NAf_POLA_masked_22_5_new_2lev.txt", header = FALSE)))
SAf_22_5_ARCT <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/barplots_histograms/monsoon_regions/data/SHMI/SAf_ARCT_masked_22_5_new_2lev.txt", header = FALSE)))
SAf_22_5_ANTA <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/barplots_histograms/monsoon_regions/data/SHMI/SAf_ANTA_masked_22_5_new_2lev.txt", header = FALSE)))

AUS_1CO2 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/barplots_histograms/monsoon_regions/data/SHMI/AUS_1CO2_masked_new.txt", header = FALSE)))
AUS_2CO2 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/barplots_histograms/monsoon_regions/data/SHMI/AUS_2CO2_masked_new.txt", header = FALSE))) #for just three points

AUS_22_5_UNIF <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/barplots_histograms/monsoon_regions/data/SHMI/AUS_UNIF_masked_22_5_new_2lev.txt", header = FALSE)))
#AUS_22_5_TROP <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/barplots_histograms/monsoon_regions/data/SHMI/AUS_UNIF_masked_22_5_new_2lev.txt", header = FALSE)))
#AUS_22_5_POLA <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/barplots_histograms/monsoon_regions/data/SHMI/AUS_UNIF_masked_22_5_new_2lev.txt", header = FALSE)))
AUS_22_5_ARCT <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/barplots_histograms/monsoon_regions/data/SHMI/AUS_ARCT_masked_22_5_new_2lev.txt", header = FALSE)))
AUS_22_5_ANTA <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/barplots_histograms/monsoon_regions/data/SHMI/AUS_ANTA_masked_22_5_new_2lev.txt", header = FALSE)))

# ###########################
# # ###########################
Change_SA_1CO2 = SA_2CO2-SA_1CO2
Change_SA_UNIF_22_5 = SA_22_5_UNIF-SA_1CO2
#Change_SA_TROP_22_5 = SA_22_5_TROP-SA_1CO2
#Change_SA_POLA_22_5 = SA_22_5_POLA-SA_1CO2
Change_SA_ARCT_22_5 = SA_22_5_ARCT-SA_1CO2
Change_SA_ANTA_22_5 = SA_22_5_ANTA-SA_1CO2

SA_mean=c((mean(Change_SA_1CO2/SA_1CO2)*100),
             (mean(Change_SA_UNIF_22_5/SA_1CO2)*100),
            # (mean(Change_SA_TROP_22_5/SA_1CO2)*100),
            # (mean(Change_SA_POLA_22_5/SA_1CO2)*100))
           (mean(Change_SA_ARCT_22_5/SA_1CO2)*100),
             (mean(Change_SA_ANTA_22_5/SA_1CO2)*100))
           
             print(SA_mean)
SA_SE=c(standard_error(Change_SA_1CO2/SA_1CO2)*100,
                                     standard_error(Change_SA_UNIF_22_5/SA_1CO2)*100,
                                   #  standard_error(Change_SA_TROP_22_5/SA_1CO2)*100,
                                   #  standard_error(Change_SA_POLA_22_5/SA_1CO2)*100)
                                    standard_error(Change_SA_ARCT_22_5/SA_1CO2)*100,
                                     standard_error(Change_SA_ANTA_22_5/SA_1CO2)*100)
Change_SAf_1CO2 = SAf_2CO2 - SAf_1CO2
Change_SAf_UNIF = SAf_22_5_UNIF - SAf_1CO2
#Change_SAf_TROP = SAf_22_5_TROP - SAf_1CO2
#Change_SAf_POLA = SAf_22_5_POLA - SAf_1CO2
Change_SAf_ARCT = SAf_22_5_ARCT - SAf_1CO2
Change_SAf_ANTA = SAf_22_5_ANTA - SAf_1CO2

SAf_mean <- c((mean(Change_SAf_1CO2 / SAf_1CO2) * 100),
              (mean(Change_SAf_UNIF / SAf_1CO2) * 100),
             # (mean(Change_SAf_TROP / SAf_1CO2) * 100),
             # (mean(Change_SAf_POLA / SAf_1CO2) * 100))
                (mean(Change_SAf_ARCT / SAf_1CO2) * 100),
                (mean(Change_SAf_ANTA / SAf_1CO2) * 100))

SAf_SE <- c(standard_error(Change_SAf_1CO2 / SAf_1CO2) * 100,
            standard_error(Change_SAf_UNIF / SAf_1CO2) * 100,
            #standard_error(Change_SAf_TROP / SAf_1CO2) * 100,
            #standard_error(Change_SAf_POLA / SAf_1CO2) * 100)
              standard_error(Change_SAf_ARCT / SAf_1CO2) * 100,
               standard_error(Change_SAf_ANTA / SAf_1CO2) * 100)

# Add SAs data calculations
Change_AUS_1CO2 = AUS_2CO2 - AUS_1CO2
Change_AUS_UNIF = AUS_22_5_UNIF - AUS_1CO2
#Change_AUS_TROP = AUS_22_5_TROP - AUS_1CO2
#Change_AUS_POLA = AUS_22_5_POLA - AUS_1CO2
Change_AUS_ARCT = AUS_22_5_ARCT - AUS_1CO2
Change_AUS_ANTA = AUS_22_5_ANTA - AUS_1CO2

AUS_mean <- c((mean(Change_AUS_1CO2 / AUS_1CO2) * 100),
             (mean(Change_AUS_UNIF / AUS_1CO2) * 100),
          #   (mean(Change_AUS_TROP / AUS_1CO2) * 100),
           #  (mean(Change_AUS_POLA / AUS_1CO2) * 100))
              (mean(Change_AUS_ARCT / AUS_1CO2) * 100),
              (mean(Change_AUS_ANTA / AUS_1CO2) * 100))

AUS_SE <- c(standard_error(Change_AUS_1CO2 / AUS_1CO2) * 100,
           standard_error(Change_AUS_UNIF / AUS_1CO2) * 100,
          # standard_error(Change_AUS_TROP / AUS_1CO2) * 100,
          # standard_error(Change_AUS_POLA / AUS_1CO2) * 100)
            standard_error(Change_AUS_ARCT / AUS_1CO2) * 100,
            standard_error(Change_AUS_ANTA / AUS_1CO2) * 100)

# Create histogram data

hist_data <- data.frame(
  Region = rep(c("NA", "NAf", "SAs"), each = 4),
  #Experiment = rep(c("UNIFORM", "TROPIC", "POLAR"), 3),
    Experiment = rep(c("CO2", "UNIFORM", "ARCTIC", "ANTARCTIC"), 3),

  Value = c((Change_SA_1CO2/SA_1CO2)*100,
    (Change_SA_UNIF_22_5/SA_1CO2)*100,
    #(Change_SA_TROP_22_5/SA_1CO2)*100,
    #(Change_SA_POLA_22_5/SA_1CO2)*100,
    (Change_SA_ARCT_22_5/SA_1CO2)*100,
    (Change_SA_ANTA_22_5/SA_1CO2)*100,
    (Change_SAf_1CO2/SAf_1CO2)*100,
    (Change_SAf_UNIF/SAf_1CO2)*100,
    #(Change_SAf_TROP/SAf_1CO2)*100,
    #(Change_SAf_POLA/SAf_1CO2)*100,
    (Change_SAf_ARCT/SAf_1CO2)*100,
    (Change_SAf_ANTA/SAf_1CO2)*100,
    (Change_AUS_1CO2/AUS_1CO2)*100,
    (Change_AUS_UNIF/AUS_1CO2)*100,
    #(Change_AUS_TROP/AUS_1CO2)*100,
    #(Change_AUS_POLA/AUS_1CO2)*100
    (Change_AUS_ARCT/AUS_1CO2)*100,
    (Change_AUS_ANTA/AUS_1CO2)*100
  )
)
# Create histogram plot
#hist_colors <- c("UNIF" = "#A6CEE3", "TROP" = "#FB9A99", "POLA" = "#33A02C")

#p_hist <- ggplot(hist_data, aes(x = Value, fill = Experiment)) +
  #geom_histogram(alpha = 0.7, position = "identity", bins = 20) +
  #facet_grid(Region ~ Experiment, scales = "free") +
 # scale_fill_manual(values = hist_colors) +
 # labs(x = "Δ Precipitation (%)", y = "Frequency", 
 #      title = "Distribution of Precipitation Changes by Region and Experiment") +
 # theme_bw() +
 # theme(
  #  strip.text = element_text(family = "Times New Roman", size = 12),
  #  axis.text = element_text(family = "Times New Roman", size = 10),
 # axis.title = element_text(family = "Times New Roman", size = 14),
  #  plot.title = element_text(family = "Times New Roman", size = 16, hjust = 0.5),
  #  legend.position = "none"
 #) +
 #geom_vline(xintercept = 0, linetype = "dashed", alpha = 0.5)

#p_hist

#ggSAsve("Monsoon_regions_histogram_UNIF_TROP_POLA.png", width = 30, height = 20, units = "cm", dpi = 300)

df_all <- data.frame(
  Region = rep(c("South America", "South Africa", "Australia"), each = 4),
  #Experiment = (c("UNIFORM", "TROPIC", "POLAR","UNIFORM", "TROPIC", "POLAR","UNIFORM", "TROPIC", "POLAR")),
    Experiment = (c("CO2","UNIFORM", "ARCTIC", "ANTARCTIC","CO2","UNIFORM", "ARCTIC", "ANTARCTIC","CO2","UNIFORM", "ARCTIC", "ANTARCTIC")),

  Cntl = rep("25.76Mt", 12),
  Mean = c(SA_mean, SAf_mean, AUS_mean),
  SE = c(SA_SE, SAf_SE, AUS_SE)
)
print(df_all)
#df_all$Experiment <- factor(df_all$Experiment, levels = c("CO2", "UNIFORM", "ARCTIC", "ANTARCTIC"))
df_all$Experiment <- factor(df_all$Experiment, levels = c("CO2", "ARCTIC",  "UNIFORM","ANTARCTIC"))

# Alternative approach if you want to keep 1XCO2 in the data frame
# df_all <- data.frame(
#   Region = c(rep("NA", 3), rep("NAf", 3), rep("SAs", 3)),
#   Experiment = rep(c("UNIFORM", "TROPIC", "POLAR"), 3),
#   Cntl = rep("25.76Mt", 9),
#   Mean = c(SA_mean, NAf_mean, AUS_mean),
#   SE = c(SA_SE, NAf_SE, AUS_SE)
# ) # names <- c("SHMI_1CO2", "SHMI_UNIF", "SHMI_TROP", "SHMI_POLA", "SHMI_ARCT", "SHMI_ANTA")

# # create a matrix with the means and standard errors
# data <- cbind(SHMI_mean, SHMI_SE)

# # create the barplot
# barplot(data[,1], names.arg=names, ylim=c(min(data), max(data)*1.2), 
#         col=rainbow(length(SHMI_mean)), main="SHMI Means", xlab="Groups", ylab="Means")

# # add the error bars
# arrows(x0=1:6, y0=data[,1]-data[,2], y1=data[,1]+data[,2], angle=90, code=3, length=0.1, 
#        lwd=1.5)
# colors <- c("1XCO2" = "black", "UNIFORM" = "#A6CEE3", "TROPIC" = "#FB9A99", "POLAR" = "#33A02C", "ARCTIC" = "#B2DF8A", "ANTARCTIC" = "#1F78B4")
#colors <- c("1XCO2" = "#4d4c4c", "25.76Mt" = "blue")
#colors <- c("CO2"="#4d4c4c","ARCTIC" = "#FB9A99", "UNIFORM" = "#A6CEE3", "ANTARCTIC" = "#33A02C")
colors <- c(
  "CO2"       = "#4D4D4D",   # Neutral dark gray – Baseline/control
  "UNIFORM"   = "#FB9A99",   # Steel blue – Well-distributed forcing
  #"TROPIC"    = "#FDBF6F",   # Vivid green – Tropical forcing
  #"POLAR"     = "#FB8072",   # Red – Polar forcing (intense, distinct)
  "ARCTIC"    = "#A6CEE3",   # Light blue – Arctic-specific, subtle
  "ANTARCTIC" = "#B2DF8A"    # Light green – Antarctic-specific, mild
)
labels <- c(
  "CO2" = expression(CO[2]~ "warming"),
  "UNIFORM" = "Uniform",
  "ARCTIC" = "Arctic",
  "ANTARCTIC" = "Antarctic"
)
# Begin plot
p <- ggplot(df_all, aes(x = Region, y = Mean, fill = Experiment)) +

  geom_bar(stat = "identity", position = position_dodge(width = 0.7), width = 0.6) +
  geom_errorbar(aes(ymin = Mean - SE, ymax = Mean + SE),
                width = 0.2, position = position_dodge(width = 0.7)) +
  scale_fill_manual(values = colors, labels = labels) +
  geom_hline(yintercept = 0, linetype = "dashed") +

  labs(x = "", y = expression(Delta~"Precipitation (%)"),
       title = "Southern Hemisphere Tropical Monsoon Regions (DJF)") +

  theme_bw() +
  theme(
    legend.title = element_blank(),
    legend.text = element_text(family = "Times New Roman", size = 20),
    #legend.position = "top",
    legend.position = c(0.65,0.1),
    legend.direction = "horizontal",
    axis.text.x = element_text(family = "Times New Roman", size = 22, color = "black"),
    axis.text.y = element_text(family = "Times New Roman", size = 22, color = "black"),
    axis.title.y = element_text(family = "Times New Roman", size = 26, color = "black"),
    plot.title = element_text(family = "Times New Roman", size = 26, hjust = 0),
    panel.border = element_rect(colour = "black", fill = NA, size = 1.2),
    panel.grid.major = element_blank(),
    panel.grid.minor = element_blank()
  ) +
 ylim(-30, 30)

# Display plot
p

# SAsve if needed
ggsave("GroupedBar_MonsoonRegions_SH_UArAN_co2.png", width = 25, height = 15, units = "cm", dpi = 300)