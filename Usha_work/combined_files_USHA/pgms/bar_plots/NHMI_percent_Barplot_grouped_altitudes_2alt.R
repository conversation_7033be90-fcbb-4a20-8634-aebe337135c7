library(ggthemes)
library(ggplot2)
library(png)
library(ncdf4)
library('reshape2')
library(we<PERSON><PERSON>on)
library(ggrepel)
library(dplyr)

mav <- function(x,n=5){stats::filter(x,rep(1/n,n), sides=2)}
standard_error <- function(x) sd(x) / sqrt(length(x))

# Load NHMI data for different altitudes
NHMI_1CO2 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/NHMI_1CO2_masked_C5.txt", header = FALSE)))
NHMI_2CO2 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/NHMI_2CO2_masked_C5.txt", header = FALSE)))
NHMI_30MT_38 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/NHMI_30MT_38hpa_masked_C5.txt", header = FALSE)))
NHMI_30MT_72 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/NHMI_30MT_72hpa_masked_C5.txt", header = FALSE)))
NHMI_30MT_103 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/NHMI_30MT_103hpa_masked_C5.txt", header = FALSE)))
NHMI_35MT_103 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/NHMI_35MT_103hpa_masked_C5.txt", header = FALSE)))

# Load SHMI data
SHMI_1CO2 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/SHMI_1CO2_masked_C5.txt", header = FALSE)))
SHMI_2CO2 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/SHMI_2CO2_masked_C5.txt", header = FALSE)))
SHMI_30MT_38 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/SHMI_30MT_38hpa_masked_C5.txt", header = FALSE)))
SHMI_30MT_72 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/SHMI_30MT_72hpa_masked_C5.txt", header = FALSE)))
SHMI_30MT_103 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/SHMI_30MT_103hpa_masked_C5.txt", header = FALSE)))
SHMI_35MT_103 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/SHMI_35MT_103hpa_masked_C5.txt", header = FALSE)))

# Load TMI data
TMI_1CO2 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/TMI_1CO2_masked_C5.txt", header = FALSE)))
TMI_2CO2 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/TMI_2CO2_masked_C5.txt", header = FALSE)))
TMI_30MT_38 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/TMI_30mt_38hpa_masked_C5.txt", header = FALSE)))
TMI_30MT_72 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/TMI_30mt_72hpa_masked_C5.txt", header = FALSE)))
TMI_30MT_103 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/TMI_30mt_103hpa_masked_C5.txt", header = FALSE)))
TMI_35MT_103 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/TMI_35mt_103hpa_masked_C5.txt", header = FALSE)))

# Load Global precipitation data
Global_1CO2 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/Global_PT_1CO2_C5.txt", header = FALSE)))
Global_2CO2 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/Global_PT_2CO2_C5.txt", header = FALSE)))
Global_30MT_38 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/Global_PT_30mt_38hpa.txt", header = FALSE)))
Global_30MT_72 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/Global_PT_30mt_72hpa.txt", header = FALSE)))
Global_30MT_103 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/Global_PT_30mt_103hpa.txt", header = FALSE)))
Global_35MT_103 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/Global_PT_35mt_103hpa.txt", header = FALSE)))

# Load India precipitation data
India_1CO2 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/India_mask_P_mean_1CO2_C5.txt", header = FALSE)))
India_2CO2 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/India_mask_P_mean_2CO2_C5.txt", header = FALSE)))
India_30MT_38 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/India_mask_P_mean_30mt_38hpa.txt", header = FALSE)))
India_30MT_72 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/India_mask_P_mean_30mt_72hpa.txt", header = FALSE)))
India_30MT_103 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/India_mask_P_mean_30mt_103hpa.txt", header = FALSE)))
India_35MT_103 <- as.numeric(unlist(read.table("/home/<USER>/Documents/backup/my_work_draft/writeups/Budget/TS_PT/Usha_work/combined_files_USHA/pgms/India_mask_P_mean_35mt_103hpa.txt", header = FALSE)))

# Calculate changes for all variables
# NHMI
Change_NHMI_1CO2 = NHMI_2CO2-NHMI_1CO2
Change_NHMI_38 = NHMI_30MT_38-NHMI_1CO2
Change_NHMI_72 = NHMI_30MT_72-NHMI_1CO2
Change_NHMI_103 = NHMI_30MT_103-NHMI_1CO2
Change_NHMI_103_35 = NHMI_35MT_103-NHMI_1CO2

# SHMI
Change_SHMI_1CO2 = SHMI_2CO2-SHMI_1CO2
Change_SHMI_38 = SHMI_30MT_38-SHMI_1CO2
Change_SHMI_72 = SHMI_30MT_72-SHMI_1CO2
Change_SHMI_103 = SHMI_30MT_103-SHMI_1CO2
Change_SHMI_103_35 = SHMI_35MT_103-SHMI_1CO2

# TMI
Change_TMI_1CO2 = TMI_2CO2-TMI_1CO2
Change_TMI_38 = TMI_30MT_38-TMI_1CO2
Change_TMI_72 = TMI_30MT_72-TMI_1CO2
Change_TMI_103 = TMI_30MT_103-TMI_1CO2
Change_TMI_103_35 = TMI_35MT_103-TMI_1CO2

# Global
Change_Global_1CO2 = Global_2CO2-Global_1CO2
Change_Global_38 = Global_30MT_38-Global_1CO2
Change_Global_72 = Global_30MT_72-Global_1CO2
Change_Global_103 = Global_30MT_103-Global_1CO2
Change_Global_103_35 = Global_35MT_103-Global_1CO2

# India
Change_India_1CO2 = India_2CO2-India_1CO2
Change_India_38 = India_30MT_38-India_1CO2
Change_India_72 = India_30MT_72-India_1CO2
Change_India_103 = India_30MT_103-India_1CO2
Change_India_103_35 = India_35MT_103-India_1CO2

# Calculate means and standard errors for all variables
NHMI_mean=c((mean(Change_NHMI_1CO2/NHMI_1CO2)*100),
            (mean(Change_NHMI_38/NHMI_1CO2)*100),
           # (mean(Change_NHMI_72/NHMI_1CO2)*100),
            (mean(Change_NHMI_103/NHMI_1CO2)*100),
            (mean(Change_NHMI_103_35/NHMI_1CO2)*100))

NHMI_SE=c(standard_error(Change_NHMI_1CO2/NHMI_1CO2)*100,
          standard_error(Change_NHMI_38/NHMI_1CO2)*100,
          #standard_error(Change_NHMI_72/NHMI_1CO2)*100,
          standard_error(Change_NHMI_103/NHMI_1CO2)*100,
          standard_error(Change_NHMI_103_35/NHMI_1CO2)*100)

SHMI_mean=c((mean(Change_SHMI_1CO2/SHMI_1CO2)*100),
            (mean(Change_SHMI_38/SHMI_1CO2)*100),
         #   (mean(Change_SHMI_72/SHMI_1CO2)*100),
            (mean(Change_SHMI_103/SHMI_1CO2)*100),
            (mean(Change_SHMI_103_35/SHMI_1CO2)*100))

SHMI_SE=c(standard_error(Change_SHMI_1CO2/SHMI_1CO2)*100,
          standard_error(Change_SHMI_38/SHMI_1CO2)*100,
         # standard_error(Change_SHMI_72/SHMI_1CO2)*100,
          standard_error(Change_SHMI_103/SHMI_1CO2)*100,
          standard_error(Change_SHMI_103_35/SHMI_1CO2)*100)

TMI_mean=c((mean(Change_TMI_1CO2/TMI_1CO2)*100),
           (mean(Change_TMI_38/TMI_1CO2)*100),
      #     (mean(Change_TMI_72/TMI_1CO2)*100),
           (mean(Change_TMI_103/TMI_1CO2)*100),
           (mean(Change_TMI_103_35/TMI_1CO2)*100))

TMI_SE=c(standard_error(Change_TMI_1CO2/TMI_1CO2)*100,
         standard_error(Change_TMI_38/TMI_1CO2)*100,
        # standard_error(Change_TMI_72/TMI_1CO2)*100,
         standard_error(Change_TMI_103/TMI_1CO2)*100,
         standard_error(Change_TMI_103_35/TMI_1CO2)*100)

Global_mean=c((mean(Change_Global_1CO2/Global_1CO2)*100),
              (mean(Change_Global_38/Global_1CO2)*100),
          #    (mean(Change_Global_72/Global_1CO2)*100),
              (mean(Change_Global_103/Global_1CO2)*100),
              (mean(Change_Global_103_35/Global_1CO2)*100))

Global_SE=c(standard_error(Change_Global_1CO2/Global_1CO2)*100,
            standard_error(Change_Global_38/Global_1CO2)*100,
        #    standard_error(Change_Global_72/Global_1CO2)*100,
            standard_error(Change_Global_103/Global_1CO2)*100,
            standard_error(Change_Global_103_35/Global_1CO2)*100)

India_mean=c((mean(Change_India_1CO2/India_1CO2)*100),
             (mean(Change_India_38/India_1CO2)*100),
      #       (mean(Change_India_72/India_1CO2)*100),
             (mean(Change_India_103/India_1CO2)*100),
             (mean(Change_India_103_35/India_1CO2)*100))

India_SE=c(standard_error(Change_India_1CO2/India_1CO2)*100,
           standard_error(Change_India_38/India_1CO2)*100,
     #      standard_error(Change_India_72/India_1CO2)*100,
           standard_error(Change_India_103/India_1CO2)*100,
           standard_error(Change_India_103_35/India_1CO2)*100)

# Create combined data frame
df_all <- data.frame(
  Variable = rep(c("NHMI", "SHMI", "TMI", "Global", "ISMI"), each = 4),
  Experiment = rep(c("CO2","UNIFORM_30MT_22km", "UNIFORM_30MT_16km","UNIFORM_35MT_16km"), 5),
  Mean = c(NHMI_mean, SHMI_mean, TMI_mean, Global_mean, India_mean),
  SE = c(NHMI_SE, SHMI_SE, TMI_SE, Global_SE, India_SE)
)
print(df_all)
df_all$Experiment <- factor(df_all$Experiment, levels = c("CO2", "UNIFORM_30MT_22km", "UNIFORM_30MT_16km","UNIFORM_35MT_16km"))
df_all$Variable <- factor(df_all$Variable, levels = c("Global", "TMI", "NHMI","SHMI","ISMI"))

# Colors and labels
colors <- c(
  "CO2" = "red",#"#4D4D4D",
  "UNIFORM_30MT_22km" = "blue",#"#FB9A99",
 # "UNIFORM_30MT_72hpa" = "#A6CEE3",
  "UNIFORM_30MT_16km" = "green",#"#B2DF8A",
  "UNIFORM_35MT_16km" = "#33A02C"
)

labels <- c(
  "CO2" = expression(CO[2]~"warming"),
  "UNIFORM_30MT_22km" = expression("SAG"["UNIF"] * "_30MT_22km"),
 # "UNIFORM_30MT_72hpa" = "Uniform_30MT_72hpa",
  "UNIFORM_30MT_16km" = expression("SAG"["UNIF"] * "_30MT_16km"),
  "UNIFORM_35MT_16km" = expression("SAG"["UNIF"] * "_35MT_16km")
)

# Create plot
p <- ggplot(df_all, aes(x = Variable, y = Mean, fill = Experiment)) +
  geom_bar(stat = "identity", position = position_dodge(width = 0.7), width = 0.6) +
  geom_errorbar(aes(ymin = Mean - SE, ymax = Mean + SE),
                width = 0.2, position = position_dodge(width = 0.7)) +
  scale_fill_manual(values = colors, labels = labels) +
  geom_hline(yintercept = 0, linetype = "dashed") +
  labs(x = "", y = expression(Delta~"Precipitation/Index (%)"),
       title = "Monsoon Indices and Precipitation Response to Different Altitude Forcing") +
  theme_bw() +
  theme(
    legend.title = element_blank(),
    legend.text = element_text(family = "Times New Roman", size = 19),
    #legend.position = "bottom",
        legend.position = c(0.5, 0.1),

    legend.direction = "horizontal",
    axis.text.x = element_text(family = "Times New Roman", size = 20, color = "black"),
    axis.text.y = element_text(family = "Times New Roman", size = 20, color = "black"),
    axis.title.y = element_text(family = "Times New Roman", size = 24, color = "black"),
    plot.title = element_text(family = "Times New Roman", size = 24, hjust = 0),
    panel.border = element_rect(colour = "black", fill = NA, size = 1.2),
    panel.grid.major = element_blank(),
    panel.grid.minor = element_blank()
  ) +
  ylim(-20, 20)

# Display plot
p

# Save plot
ggsave("GroupedBar_MonsoonIndices_Global_India_altitudes_2alt.png", width = 30, height = 20, units = "cm", dpi = 300)
