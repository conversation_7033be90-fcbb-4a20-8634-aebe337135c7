load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_code.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/contributed.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/shea_util.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/wrf/WRFUserARW.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/wrf/WRF_contributed.ncl"

begin
;*************************************************

; e_path="/home/<USER>/Documents/backup/my_work_draft/writeups/hydrological_cycle"
e_path="/home/<USER>/Documents/backup/my_work_draft/writeups/hydrological_cycle"
e_1CO2 = addfile("/home/<USER>/Documents/backup/my_work_draft/ITCZ/Yearly_E_CO2_01_100new1.nc", "r")
e_2CO2 = addfile("/home/<USER>/Documents/backup/my_work_draft/ITCZ/Yearly_E2000_2CO2_01_100new1.nc", "r")
e_UNIF_22_5 = addfile("/home/<USER>/Documents/backup/my_work_draft/ITCZ/Yearly_E2000_2CO2_UNIF_01_100_2D_1.nc", "r")
e_SOLAR = addfile("/home/<USER>/Documents/Data_solin/29_4_solin/E2000_solin_29_4/Yearly_E2000_solin_29_4_01_48_1.nc", "r")
e_TROP = addfile("/home/<USER>/Documents/backup/my_work_draft/ITCZ/Yearly_E2000_2CO2_TROP_01_100_2D_1.nc","r")
e_POLA = addfile("/home/<USER>/Documents/backup/my_work_draft/ITCZ/Yearly_E2000_2CO2_POLAR_01_100_2D_1.nc","r")


; Get latitude and longitude
lat = e_1CO2->lat
lon = e_1CO2->lon



; Calculate surface temperature differences with area weighting
; First average over time dimension
TS_2CO2_time_avg = dim_avg_n_Wrap(e_2CO2->TS(40:99,:,:), 0)
TS_1CO2_time_avg = dim_avg_n_Wrap(e_1CO2->TS(40:99,:,:), 0)
TS_UNIF_22_5_time_avg = dim_avg_n_Wrap(e_UNIF_22_5->TS(40:99,:,:), 0)
TS_SOLAR_time_avg = dim_avg_n_Wrap(e_SOLAR->TS(40:47,:,:), 0)
TS_TROP_time_avg = dim_avg_n_Wrap(e_TROP->TS(40:99,:,:), 0)
TS_POLA_time_avg = dim_avg_n_Wrap(e_POLA->TS(40:99,:,:), 0)

PT_2CO2_time_avg = dim_avg_n_Wrap(e_2CO2->PRECT(40:99,:,:), 0)* 86400.0 
PT_1CO2_time_avg = dim_avg_n_Wrap(e_1CO2->PRECT(40:99,:,:), 0)* 86400.0 
PT_UNIF_22_5_time_avg = dim_avg_n_Wrap(e_UNIF_22_5->PRECT(40:99,:,:), 0)* 86400.0 
PT_SOLAR_time_avg = dim_avg_n_Wrap(e_SOLAR->PRECT(40:47,:,:), 0)* 86400.0
PT_TROP_time_avg = dim_avg_n_Wrap(e_TROP->PRECT(40:99,:,:), 0)* 86400.0 
PT_POLA_time_avg = dim_avg_n_Wrap(e_POLA->PRECT(40:99,:,:), 0)* 86400.0

; Compute zonal means (longitude-weighted average at each latitude)
rad = 4.0*atan(1.0)/180.0  ; Convert degrees to radians
clat = cos(lat * rad)   ; latitude weights (not used in this simplified version)

; Compute zonal means (longitude-weighted average at each latitude)
rad = 4.0*atan(1.0)/180.0  ; Convert degrees to radians
clat = cos(lat * rad)   ; latitude weights (not used in this simplified version)
wgt = cos(lat * rad)
ts_global_1co2 = wgt_areaave_Wrap(TS_1CO2_time_avg, wgt, 1.0, 0)  ; scalar
ts_global_2co2 = wgt_areaave_Wrap(TS_2CO2_time_avg, wgt, 1.0, 0)  ; scalar
ts_global_UNIF = wgt_areaave_Wrap(TS_UNIF_22_5_time_avg, wgt, 1.0, 0)  ; scalar
ts_global_SOLAR = wgt_areaave_Wrap(TS_SOLAR_time_avg, wgt, 1.0, 0)  ; scalar
ts_global_TROP = wgt_areaave_Wrap(TS_TROP_time_avg, wgt, 1.0, 0)  ; scalar
ts_global_POLA = wgt_areaave_Wrap(TS_POLA_time_avg, wgt, 1.0, 0)  ; scalar

; Calculate zonal mean precipitation
PT_1CO2_zonal = dim_avg_n_Wrap(PT_1CO2_time_avg, 1)
PT_2CO2_zonal = dim_avg_n_Wrap(PT_2CO2_time_avg, 1)
PT_UNIF_zonal = dim_avg_n_Wrap(PT_UNIF_22_5_time_avg, 1)
PT_SOLAR_zonal = dim_avg_n_Wrap(PT_SOLAR_time_avg, 1)
PT_TROP_zonal = dim_avg_n_Wrap(PT_TROP_time_avg, 1)
PT_POLA_zonal = dim_avg_n_Wrap(PT_POLA_time_avg, 1)

; Calculate precipitation differences
PT_diff_2CO2_1CO2 = PT_2CO2_zonal - PT_1CO2_zonal
PT_diff_1CO2_2CO2 = PT_1CO2_zonal - PT_2CO2_zonal
PT_diff_UNIF_2CO2 = PT_UNIF_zonal - PT_2CO2_zonal
PT_diff_SOLAR_2CO2 = PT_SOLAR_zonal - PT_2CO2_zonal
PT_diff_TROP_2CO2 = PT_TROP_zonal - PT_2CO2_zonal
PT_diff_POLA_2CO2 = PT_POLA_zonal - PT_2CO2_zonal

;percentage
PT_diff_1CO2_2CO2_pcnt=(PT_diff_1CO2_2CO2/PT_2CO2_zonal)*100
PT_diff_2CO2_1CO2_pcnt=(PT_diff_2CO2_1CO2/PT_1CO2_zonal)*100
PT_diff_UNIF_2CO2_pcnt=(PT_diff_UNIF_2CO2/PT_2CO2_zonal)*100
PT_diff_SOLAR_2CO2_pcnt=(PT_diff_SOLAR_2CO2/PT_2CO2_zonal)*100
PT_diff_TROP_2CO2_pcnt=(PT_diff_TROP_2CO2/PT_2CO2_zonal)*100
PT_diff_POLA_2CO2_pcnt=(PT_diff_POLA_2CO2/PT_2CO2_zonal)*100

; Use global mean temperatures for normalization
PT_per_K_2CO2_1CO2 = PT_diff_2CO2_1CO2_pcnt / abs(ts_global_2co2-ts_global_1co2)
PT_per_K_1CO2_2CO2 = PT_diff_1CO2_2CO2_pcnt / abs(ts_global_1co2-ts_global_2co2)
PT_per_K_UNIF_2CO2 = PT_diff_UNIF_2CO2_pcnt / abs(ts_global_UNIF-ts_global_2co2)
PT_per_K_SOLAR_2CO2 = PT_diff_SOLAR_2CO2_pcnt / abs(ts_global_SOLAR-ts_global_2co2)
PT_per_K_TROP_2CO2 = PT_diff_TROP_2CO2_pcnt / abs(ts_global_TROP-ts_global_2co2)
PT_per_K_POLA_2CO2 = PT_diff_POLA_2CO2_pcnt / abs(ts_global_POLA-ts_global_2co2)

printVarSummary(ts_global_1co2)
printVarSummary(ts_global_2co2)
printVarSummary(ts_global_UNIF)
printVarSummary(ts_global_SOLAR)
printVarSummary(ts_global_TROP)

aa=dim_avg_n_Wrap(PT_per_K_2CO2_1CO2,0)
bb=dim_avg_n_Wrap(PT_per_K_1CO2_2CO2, 0)
print(aa)
print(bb)
cc=ts_global_1co2-ts_global_2co2
print(cc)

; Create a multi-dimensional array for temperature plotting
tempPTPlotData = new((/6, dimsizes(lat)/), float)
tempPTPlotData(0,:) = PT_per_K_2CO2_1CO2  ; 2CO2 
tempPTPlotData(1,:) = PT_per_K_1CO2_2CO2  ; 1CO2 
tempPTPlotData(2,:) = PT_per_K_UNIF_2CO2 ; UNIF
tempPTPlotData(3,:) = PT_per_K_TROP_2CO2  ; TROP
tempPTPlotData(4,:) = PT_per_K_POLA_2CO2  ; POLA
tempPTPlotData(5,:) = PT_per_K_SOLAR_2CO2  ; SOLAR

tempPTPlotData1=tempPTPlotData;*10e4
print(tempPTPlotData(1,:))
; Set up plot resources
wks = gsn_open_wks("pdf","PTperK_vs_lat_profiles2_changes_TROP_2CO2")

res = True
res@gsnDraw           = True
res@gsnFrame          = True
res@xyLineColors      = (/"red","black","blue","orange","green","MAGENTA"/)
res@xyLineThicknesses = (/2.5, 2.5, 2.5,2.5,2.5,2.5/)
res@xyDashPatterns    = (/0, 0,0,0,0,0/)         ; all solid lines
res@gsnXYTopLabel     = False
res@tiMainString      = "Precipitation per unit warming vs Latitude"
;res@tiYAxisString     = "Precipitation per unit warming (10~S~-5~N~mm/day/K)"
res@tiYAxisString     = "Precipitation per unit warming (%/K)"

res@tiXAxisString     = "Latitude (~F34~0~F~)"
res@trXMinF           = -75
res@trXMaxF           = 75

res@vpHeightF         = 0.6
res@vpWidthF          = 0.75

res@lgLabelFontHeightF = 0.015
res@xyExplicitLegendLabels = (/"2xCO~B~2~N~-1xCO~B~2 ","1xCO~B~2~N~-2xCO~B~2 ", "SAI-2xCO~B~2", "TROP-2xCO~B~2", "POLA-2xCO~B~2","SOLAR-2xCO~B~2"/)
res@pmLegendDisplayMode = "Always"
res@pmLegendSide = "Top"     ; or "Bottom", "Top", etc.
res@pmLegendParallelPosF = 0.8 ; fine-tune positioning
res@pmLegendWidthF         =  0.25                       ;-- define legend width
res@pmLegendHeightF        =  0.15
res@pmLegendOrthogonalPosF = -0.40
res@lgPerimOn = True           ; legend box border

; Create the plot
plot = gsn_csm_xy(wks, lat, tempPTPlotData1, res)

exit()

end



