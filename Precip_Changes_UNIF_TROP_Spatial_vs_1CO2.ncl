; Script to plot precipitation changes spatial patterns: UNIF-1CO2 global, UNIF-1CO2 tropics, and TROP-1CO2 tropics

load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_code.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/contributed.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/shea_util.ncl"

begin
  ; Define paths and load data files
  e_path="/home/<USER>/Documents/backup/my_work_draft/writeups/hydrological_cycle"
  e_1CO2 = addfile(e_path+"/Yearly_E_CO2_01_100new1.nc", "r")
  e_UNIF = addfile("/home/<USER>/Documents/backup/my_work_draft/ITCZ/Yearly_E_37_UNIF_01_100_new1.nc", "r")
  e_TROP = addfile("/home/<USER>/Documents/backup/my_work_draft/ITCZ/Yearly_E2000_2CO2_TROP_01_100_2D_1.nc", "r")
  
  ; Get latitude and longitude
  lat = e_1CO2->lat
  lon = e_1CO2->lon
  
  ; Add units to lat and lon
  lat@units = "degrees_north"
  lon@units = "degrees_east"
  
  ; Calculate precipitation differences with area weighting
  ; First average over time dimension (using years 40-99 for steady state)
  PRECT_1CO2_time_avg = dim_avg_n_Wrap(e_1CO2->PRECT(40:99,:,:), 0)
  PRECT_UNIF_time_avg = dim_avg_n_Wrap(e_UNIF->PRECT(40:99,:,:), 0)
  PRECT_TROP_time_avg = dim_avg_n_Wrap(e_TROP->PRECT(40:99,:,:), 0)
  
  ; Convert from m/s to mm/day (multiply by 86400*1000)
  PRECT_1CO2_time_avg = PRECT_1CO2_time_avg * 86400000.0
  PRECT_UNIF_time_avg = PRECT_UNIF_time_avg * 86400000.0
  PRECT_TROP_time_avg = PRECT_TROP_time_avg * 86400000.0
  
  ; Calculate differences with respect to 1CO2
  PRECT_diff_UNIF = PRECT_UNIF_time_avg - PRECT_1CO2_time_avg
  PRECT_diff_TROP = PRECT_TROP_time_avg - PRECT_1CO2_time_avg
  
  ; Copy coordinate information
  copy_VarCoords(PRECT_1CO2_time_avg, PRECT_diff_UNIF)
  copy_VarCoords(PRECT_1CO2_time_avg, PRECT_diff_TROP)
  
  ; Ensure coordinate units are set
  PRECT_diff_UNIF&lat@units = "degrees_north"
  PRECT_diff_UNIF&lon@units = "degrees_east"
  PRECT_diff_TROP&lat@units = "degrees_north"
  PRECT_diff_TROP&lon@units = "degrees_east"
  
  ; Calculate global and tropical mean precipitation changes using wgt_areaave_Wrap
  rad = 4.0*atan(1.0)/180.0
  wgt = cos(lat * rad)
  
  ; Global means
  prect_diff_global_UNIF = wgt_areaave_Wrap(PRECT_diff_UNIF, wgt, 1.0, 0)
  
  ; Tropical means (30S-30N)
  lat_indices = ind(lat.ge.-30 .and. lat.le.30)
  prect_diff_tropical_UNIF = wgt_areaave_Wrap(PRECT_diff_UNIF(lat_indices,:), wgt(lat_indices), 1.0, 0)
  prect_diff_tropical_TROP = wgt_areaave_Wrap(PRECT_diff_TROP(lat_indices,:), wgt(lat_indices), 1.0, 0)
  
  ; Create labels for mean values
  Label_Global_UNIF = "Global Mean="+decimalPlaces(prect_diff_global_UNIF,2,True)+ " mm/day"
  Label_Tropical_UNIF = "Tropical Mean="+decimalPlaces(prect_diff_tropical_UNIF,2,True)+ " mm/day"
  Label_Tropical_TROP = "Tropical Mean="+decimalPlaces(prect_diff_tropical_TROP,2,True)+ " mm/day"
  
  print("Global mean precipitation difference (UNIF - 1xCO2): " + prect_diff_global_UNIF + " mm/day")
  print("Tropical mean precipitation difference (UNIF - 1xCO2): " + prect_diff_tropical_UNIF + " mm/day")
  print("Tropical mean precipitation difference (TROP - 1xCO2): " + prect_diff_tropical_TROP + " mm/day")
  
  ; Create plots
  wks = gsn_open_wks("png", "Precip_Changes_UNIF_TROP_Spatial_vs_1CO2")
  
  ; Common resources for all plots
  res = True
  res@gsnDraw = False
  res@gsnFrame = False
  res@gsnMaximize = True
  res@gsnAddCyclic = True
  
  res@mpFillOn = False
  res@mpOutlineOn = True
  res@mpCenterLonF = 180.0
  
  ; Add grid lines
  res@mpGridAndLimbOn = True
  res@mpGridLatSpacingF = 30
  res@mpGridLonSpacingF = 60
  res@mpGridLineColor = "gray"
  res@mpGridLineThicknessF = 0.5
  res@mpGridLineDashPattern = 2
  
  ; Make sure longitude labels are displayed
  res@tmXBLabelsOn = True
  res@tmXBOn = True
  res@tmXTOn = False
  
  res@cnFillOn = True
  res@cnLinesOn = False
  res@cnLineLabelsOn = False
  res@cnInfoLabelOn = False
  res@lbLabelBarOn = False  ; Turn off individual label bars
  
  res@tmXBLabelFontHeightF    = 0.018    ; Make these labels smaller.
  res@tmYLLabelFontHeightF    = 0.018   ; Make these labels smaller.

  ; Use a diverging color map for precipitation
  gsn_define_colormap(wks, "MPL_BrBG")

  res@cnLevelSelectionMode = "ManualLevels"
  res@cnMinLevelValF = -2.0
  res@cnMaxLevelValF = 2.0
  res@cnLevelSpacingF = 0.5
  
  ; Create panel plot array
  plot = new(3, graphic)

  ; Set consistent aspect ratio for all plots
  res@vpWidthF = 0.9    ; Set width (in NDC units: 0 to 1)
  res@vpHeightF = 0.45  ; Set height; adjust to preserve map proportions

  ; Plot a) Global UNIF - 1CO2
  res@mpMaxLatF = 90
  res@mpMinLatF = -90
  res@gsnLeftString = "a) Global UNIF - 1CO~B1~2"
  res@gsnRightString = Label_Global_UNIF
  res@gsnStringFontHeightF = 0.02
  plot(0) = gsn_csm_contour_map(wks, PRECT_diff_UNIF, res)

  ; Plot b) Tropical UNIF - 1CO2 (30S-30N)
  res@mpMaxLatF = 30
  res@mpMinLatF = -30
  res@gsnLeftString = "b) Tropical UNIF - 1CO~B1~2 (30S-30N)"
  res@gsnRightString = Label_Tropical_UNIF
  res@gsnStringFontHeightF = 0.02
  plot(1) = gsn_csm_contour_map(wks, PRECT_diff_UNIF, res)

  ; Plot c) Tropical TROP - 1CO2 (30S-30N)
  res@mpMaxLatF = 30
  res@mpMinLatF = -30
  res@gsnLeftString = "c) Tropical TROP - 1CO~B1~2 (30S-30N)"
  res@gsnRightString = Label_Tropical_TROP
  res@gsnStringFontHeightF = 0.02
  plot(2) = gsn_csm_contour_map(wks, PRECT_diff_TROP, res)
  
  ; Panel plot resources
  resP = True
  resP@gsnMaximize = True
  resP@gsnPanelLabelBar = True
  
  ; Adjust panel settings
  resP@pmLabelBarOrthogonalPosF = -0.05
  resP@gsnPanelYWhiteSpacePercent = 3.0
  resP@gsnPanelXWhiteSpacePercent = 5.0
  
  resP@gsnPanelTop = 0.98
resP@gsnPanelBottom = 0.02
  ; Colorbar settings
  resP@lbLabelFontHeightF = 0.012
  resP@lbTitleFontHeightF = 0.014
  resP@lbLabelStride = 1
  resP@lbBoxEndCapStyle = "TriangleBothEnds"
  resP@lbOrientation = "Horizontal"
  resP@pmLabelBarWidthF = 0.7
  resP@pmLabelBarHeightF = 0.08
  resP@lbTitleString = "Precipitation Change (mm/day)"
  resP@lbTitleFont = "times-roman"
  resP@lbTitlePosition = "Bottom"
  resP@lbTitleFontHeightF = 0.012
  resP@lbLabelFontHeightF = 0.01
  resP@lbLabelBarOn = True
  
  ; Create the panel plot (3 plots in a column)
  gsn_panel(wks, plot, (/3,1/), resP)
  
end
