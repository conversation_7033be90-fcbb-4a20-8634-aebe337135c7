



load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_code.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/contributed.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/shea_util.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/wrf/WRFUserARW.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/wrf/WRF_contributed.ncl"



;---Recreating jpeg images only works for X11 and PNG.
;---Recreating jpeg images only works for X11 and PNG.
   wks_type = "png"
   wks_type@wkWidth = 2500
   wks_type@wkHeight = 2500 
   pname = "plot_TS_change"
   wks = gsn_open_wks("eps",pname)
;_______________________________________________________________________________________



begin
;*************************************************
e_path="/home/<USER>/Documents/backup/my_work_draft/writeups/hydrological_cycle"

 
e_1CO2 = addfile(e_path+"/Yearly_E_CO2_01_100new1.nc", "r")
e_2CO2 = addfile(e_path+"/Yearly_E_2CO2_01_100new1.nc", "r")
e_UNIF = addfile("/home/<USER>/Documents/backup/my_work_draft/ITCZ/Yearly_E_37_UNIF_01_100_new1.nc", "r")
e_EQUI = addfile("/home/<USER>/Documents/backup/my_work_draft/ITCZ/Yearly_E2000_2CO2_TROP_01_100_2D_1.nc", "r")
e_POLA = addfile("/home/<USER>/Documents/backup/my_work_draft/ITCZ/Yearly_E2000_2CO2_POLAR_01_100_2D_1.nc", "r")
e_ARCT = addfile("/home/<USER>/Documents/backup/my_work_draft/ITCZ/Yearly_E2000_2CO2_ARCTIC_01_100_2D_1.nc", "r")
e_ANTA = addfile("/home/<USER>/Documents/backup/my_work_draft/ITCZ/Yearly_E2000_2CO2_ANTARC_01_100_2D_1.nc", "r")


 

TS_e_1CO2=e_1CO2->TS(40:99,:,:)
TS_e_2CO2=e_2CO2->TS(40:99,:,:)
TS_e_UNIF=e_UNIF->TS(40:99,:,:)
TS_e_EQUI=e_EQUI->TS(40:99,:,:)
TS_e_POLA=e_POLA->TS(40:99,:,:)
TS_e_ARCT=e_ARCT->TS(40:99,:,:)
TS_e_ANTA=e_ANTA->TS(40:99,:,:)
 
 
;---------------------------------------------------------
dummy=e_1CO2->TS(0:6,:,:)
;dummy@_FillValue = 1e+36
dummy@_FillValue =-999.999

;dummy@_FillValue = getFillValue(TS_e_1CO2)
;print(getFillValue(TS_e_1CO2))

TS_mean=new((/7,96,144/),typeof(dummy),dummy@_FillValue)
TS_mean(0,:,:)=dim_avg_n_Wrap(TS_e_2CO2,0)
TS_mean(1,:,:)=dim_avg_n_Wrap(TS_e_1CO2,0)
TS_mean(2,:,:)=dim_avg_n_Wrap(TS_e_UNIF,0)
TS_mean(3,:,:)=dim_avg_n_Wrap(TS_e_EQUI,0)
TS_mean(4,:,:)=dim_avg_n_Wrap(TS_e_POLA,0)
TS_mean(5,:,:)=dim_avg_n_Wrap(TS_e_ARCT,0)
TS_mean(6,:,:)=dim_avg_n_Wrap(TS_e_ANTA,0)
copy_VarCoords(dummy,TS_mean)
printVarSummary(TS_mean)

TS_variance=new((/7,96,144/),typeof(dummy),dummy@_FillValue)
TS_variance(0,:,:)=dim_variance_n_Wrap(TS_e_2CO2,0)
TS_variance(1,:,:)=dim_variance_n_Wrap(TS_e_1CO2,0)
TS_variance(2,:,:)=dim_variance_n_Wrap(TS_e_UNIF,0)
TS_variance(3,:,:)=dim_variance_n_Wrap(TS_e_EQUI,0)
TS_variance(4,:,:)=dim_variance_n_Wrap(TS_e_POLA,0)
TS_variance(5,:,:)=dim_variance_n_Wrap(TS_e_ARCT,0)
TS_variance(6,:,:)=dim_variance_n_Wrap(TS_e_ANTA,0)
copy_VarCoords(dummy,TS_variance)
;---------------------------------------------------------------
dummy2=e_1CO2->TS(0:5,:,:)
dummy2@_FillValue =-999.999

Change_TS=new((/6,96,144/),typeof(dummy2),dummy2@_FillValue)
Change_TS(0,:,:)=TS_mean(1,:,:)-TS_mean(0,:,:)
Change_TS(1,:,:)=TS_mean(2,:,:)-TS_mean(0,:,:)
Change_TS(2,:,:)=TS_mean(3,:,:)-TS_mean(0,:,:)
Change_TS(3,:,:)=TS_mean(4,:,:)-TS_mean(0,:,:)
Change_TS(4,:,:)=TS_mean(5,:,:)-TS_mean(0,:,:)
Change_TS(5,:,:)=TS_mean(6,:,:)-TS_mean(0,:,:)
copy_VarCoords(dummy2,Change_TS)

 
prob_TS=new((/6,96,144/),typeof(dummy),dummy@_FillValue)
prob_TS(0,:,:)=ttest(TS_mean(0,:,:),TS_variance(0,:,:),60,TS_mean(1,:,:),TS_variance(1,:,:),60,False,False)
prob_TS(1,:,:)=ttest(TS_mean(0,:,:),TS_variance(0,:,:),60,TS_mean(2,:,:),TS_variance(2,:,:),60,False,False)
prob_TS(2,:,:)=ttest(TS_mean(0,:,:),TS_variance(0,:,:),60,TS_mean(3,:,:),TS_variance(3,:,:),60,False,False)
prob_TS(3,:,:)=ttest(TS_mean(0,:,:),TS_variance(0,:,:),60,TS_mean(4,:,:),TS_variance(4,:,:),60,False,False)
prob_TS(4,:,:)=ttest(TS_mean(0,:,:),TS_variance(0,:,:),60,TS_mean(5,:,:),TS_variance(5,:,:),60,False,False)
prob_TS(5,:,:)=ttest(TS_mean(0,:,:),TS_variance(0,:,:),60,TS_mean(6,:,:),TS_variance(6,:,:),60,False,False)

copy_VarCoords(dummy2,prob_TS)
alpha= (1-prob_TS)*100 
copy_VarCoords(dummy2,alpha)

Change_sig=mask(Change_TS,(alpha.ge.98),True) ;90% 
copy_VarCoords(dummy2,Change_sig)


;write mask array to new file
;system("rm -f alpha.nc")
;fout = addfile("alpha.nc","c")
;fout->alpha   = alpha
;fout->prob = prob_TS
;fout->TS_variance = TS_variance
;fout->Change_TS=Change_TS
;fout->TS_mean=TS_mean
 

;---------------------------------------------------------

;******************************************                 Areal average
lat=e_1CO2->lat
lon=e_1CO2->lon


  jlat  = dimsizes( lat )
  rad    = 4.0*atan(1.0)/180.0
  re     = 6371220.0
  rr     = re*rad
  dlon   = abs(lon(2)-lon(1))*rr
  dx    = dlon*cos(lat*rad)
  dy     = new ( jlat, typeof(dx))
  dy(0)  = abs(lat(2)-lat(1))*rr
  dy(1:jlat-2)  = abs(lat(2:jlat-1)-lat(0:jlat-3))*rr*0.5   
  dy(jlat-1)    = abs(lat(jlat-1)-lat(jlat-2))*rr
  area   = dx*dy
  clat   = cos(lat*rad)
;****************************************************       

;GlobalMean_1CO2_e          = wgt_areaave_Wrap(TS_mean(0,:,:),area,1.0,1)
;GlobalMean_2CO2_e          = wgt_areaave_Wrap(TS_mean(1,:,:),area,1.0,1)
;GlobalMean_UNIF_e          = wgt_areaave_Wrap(TS_mean(2,:,:),area,1.0,1)
;GlobalMean_EQUI_e          = wgt_areaave_Wrap(TS_mean(3,:,:),area,1.0,1)
;GlobalMean_POLA_e          = wgt_areaave_Wrap(TS_mean(4,:,:),area,1.0,1)
;GlobalMean_ARCT_e          = wgt_areaave_Wrap(TS_mean(5,:,:),area,1.0,1)
;GlobalMean_ANTA_e          = wgt_areaave_Wrap(TS_mean(6,:,:),area,1.0,1)
 
Global_mean=wgt_areaave_Wrap(TS_mean,area,1.0,0)
copy_VarCoords(dummy2,Global_mean)

print(Global_mean)

dummy3=e_1CO2->TS(0:5,0,0)
GlobalMean_Change_TS=new((/6/),typeof(dummy3),dummy3@_FillValue)
GlobalMean_Change_TS(0)=Global_mean(1)-Global_mean(0)
GlobalMean_Change_TS(1)=Global_mean(2)-Global_mean(1)
GlobalMean_Change_TS(2)=Global_mean(3)-Global_mean(1)
GlobalMean_Change_TS(3)=Global_mean(4)-Global_mean(1)
GlobalMean_Change_TS(4)=Global_mean(5)-Global_mean(1)
GlobalMean_Change_TS(5)=Global_mean(6)-Global_mean(1)

copy_VarCoords(dummy2,GlobalMean_Change_TS)

 
 
;---------------------------------------------------------------
    
Label_Global_mean=("Mean="+decimalPlaces(GlobalMean_Change_TS,2,True)+"K")
print(Label_Global_mean)

;------------------------------------------------------------------------------------------------------------------------------- 
 
   Change_sig&lat@units="degrees_north"
   Change_sig&lon@units="degrees_east"

;------------------------------------------------ 

;___________________________________________________________________________________________________________________

  ;gsn_define_colormap(wks,"testcmap") ; GMT_relief_oceanonly cmp_b2r
  gsn_define_colormap(wks,"WhiteBlue") ; GMT_relief_oceanonly ViBlGrWhYeOrRe
  gsn_reverse_colormap(wks)

  cnres                             = True
  cnres@gsnMaximize                 = False
  ;cnres@cnFillDrawOrder             = "PreDraw"       ; draw contours before continents
  cnres@gsnDraw                     = False
  cnres@gsnFrame                    = False
  cnres@cnLinesOn                   = False
  cnres@cnLineThicknessF            = 0.5
  cnres@cnLineLabelsOn              = False
  cnres@cnFillOn                    = True

  ;cnres@mpFillOn                    = False
  ;cnres@mpGeophysicalLineColor      = "black"
  ;cnres@mpGeophysicalLineThicknessF = 1
  cnres@mpLandFillColor              = "white" ;"cornsilk" ;"black"      ;darkolivegreen ;goldenrod4
  ;cnres@mpLandFillPattern           = 4
  cnres@gsnAddCyclic                 = True

  cnres@mpCenterLonF                  =180
  ;cnres@mpLimitMode                  = "LatLon"
  ;cnres@mpMinLatF                    = -60
  ;cnres@mpMaxLatF                    = 60
  ;cnres@mpMinLonF                    = 0
  ;cnres@mpMaxLonF                    = 360
  cnres@cnLevelSelectionMode         = "ManualLevels"
  cnres@cnMinLevelValF               = -8.0
  cnres@cnMaxLevelValF               = 0.0
  cnres@cnLevelSpacingF              = 0.5
  cnres@lbLabelStride                = 2
  cnres@gsnRightStringFontHeightF    = -0.033
  cnres@gsnLeftStringFontHeightF     = -0.033

  cnres@gsnRightString               = ""
  cnres@gsnLeftString                = ""
  cnres@tiMainString                 = "" ;
  cnres@tiMainFont                   = "times-roman"
  cnres@lbLabelBarOn                 = False            ; turn off individual cb's
  cnres@lbBoxEndCapStyle             = "TriangleBothEnds"
  ;cnres@lbBoxEndCapStyle            = "TriangleHighEnd"
  cnres@pmLabelBarWidthF             = 0.04
  cnres@pmLabelBaSSTeightF           = 0.23
  cnres@lbOrientation                = "Vertical"     ; vertical label bar

  cnres@pmTickMarkDisplayMode = "Always"            ; turn on built-in tickmarks
  cnres@lbTitleOn             = True
  cnres@lbLabelStride         = 2
  cnres@lbTitleString         = "P (mm/day)" ;"kJ/cm^2"
  cnres@lbTitleFontHeightF    = 0.022
  cnres@lbTitleFont           = "times-roman"

  cnres@tmXBLabelFontHeightF    = 0.022    ; Make these labels smaller.
  cnres@tmYLLabelFontHeightF    = 0.022    ; Make these labels smaller.


  cnres@tiYAxisFont 	     = "times-roman"
  cnres@tiXAxisFont          = "times-roman"


  
   cnres@tmXBMajorLengthF     = 0.01
   cnres@tmYLMajorLengthF     = 0.01


  cnres@tmYLLabelFont                  = "times-roman"
  cnres@tmXBLabelFont                  = "times-roman"
  cnres@tmXBLabelsOn                   = False

  cnres@tmYRBorderOn = False
  cnres@tmYLBorderOn = False
  cnres@tmXBBorderOn = False
  cnres@tmXTBorderOn = False

  cnres@tmYRLabelsOn = True
  cnres@tmXBMajorLengthF=0.03
  cnres@tmYRMajorLengthF=0.03
  cnres@tmXBOn               = False     ; Turn off top tickmarks
  cnres@tmXTOn               = False     ; Turn off top tickmarks
  ;cnres@tmYLOn               = False     ; Turn off left tickmarks
  ;cnres@tmYROn               = True      ; Turn off bottom tickmarks

  cnres@tmXBMajorOutwardLengthF =-0.02
  cnres@tmXBLabelStride =2
  cnres@tmYLLabelStride =2


  ;cnres@gsnLeftString        = ""
  ;cnres@gsnLeftStringFontHeightF = 0.019


  ;cnres@gsnZonalMean                = True         ; add zonal plot
  ;cnres@gsnZonalMeanXMinF           = -1.          ; set minimum X-axis value for zonal mean plot  
  ;cnres@gsnZonalMeanXMaxF           = 1.           ; set maximum X-axis value for zonal mean plot  
  ;cnres@gsnZonalMeanYRefLine        = 0.0          ; set reference line X-axis value



  ;cnres@vpWidthF              =  1.0
  ;cnres@vpHeightF             =  1.0
  ;cnres@vpXF                  =  0.05
  ;cnres@vpYF                  =  0.95

  dlon = 30
  dlat = 30

  cnres@mpProjection          = "Robinson"
  cnres@mpPerimOn             =  True
  cnres@mpGridAndLimbOn       =  True
  cnres@mpGridLatSpacingF     =  dlat
  cnres@mpGridLonSpacingF     =  dlon
  cnres@mpGridLineColor       = "gray"

  cnres@cnConstFEnableFill =True


  ;cnres@lbBoxMinorExtentF     =  0.15                   ;-- decrease height of labelbar
;------------------------------------------------ 
zres              = True
zres@gsnDraw                     = False
zres@gsnFrame                    = False

;zres@gsnMaximize = False
zres@tmXBMode     = "Explicit"                ; Define own tick mark labels.
zres@tmXBValues   = (/-10.,0,5./)
zres@tmXBLabels   = zres@tmXBValues + ""   ;
zres@tmXBLabelFontColor   = "firebrick1" 


zres@trXMinF                 = -10.0      ; Could also use gsnZonalMeanXMinF
zres@trXMaxF                 = 5.0      ; Could also use gsnZonalMeanXMaxF

zres@xyLineThicknessF        = 3.5
zres@xyLineColor             =  "firebrick1" ;mediumslateblue
zres@gsnXRefLine             = 0
zres@gsnZonalMeanYRefLine    = 40
zres@gsnYRefLineThicknessesF = 0.1
zres@gsnYRefLineDashPatterns = 2
zres@gsnYRefLineColor        = "grey53"

;zres@gsnYAxisIrregular2Log   = True

zres@tiMainString              = "" 
zres@gsnRightString            = ""
zres@gsnRightStringFont        = "times-roman"
zres@gsnRightStringFontHeightF = 0.022

 


zres@tmXTOn                   = False      ; Turn off top tickmarks
zres@tmYLOn                   = False     ; Turn off bottom tickmarks
zres@tmYROn                   = False      ; Turn off bottom tickmarks
;zres@tmXBLabelAngleF          =45
zres@tmYRLabelsOn = False


zres@tmXBLabelFontThicknessF = 1.5
zres@tmXBLabelFontHeightF    = 0.030    ; Make these labels smaller.
zres@tmYRLabelFontHeightF    = 0.030    ; Make these labels smaller.
zres@tmXBLabelFont           = "times-roman"
zres@tmYRLabelFont           = "times-roman"

zres@tmYRMinorOn = False

zres@tiYAxisFont = "times-roman"
zres@tiYAxisString = "Latitude"
zres@tiYAxisSide = "Right"

zres@tiYAxisFontHeightF = 0.08

   ;zres@tmXBMajorLengthF     = 0.02
   ;zres@tmYLMajorLengthF     = 0.008
   ;zres@tmYRMinorLengthF     = 0.00

zres@tmYRMajorOutwardLengthF =-0.01
zres@tmYRLabelStride =6

zres@gsnFrame = False




zres@vpWidthF               =  0.2
;zres@vpHeightF             =  0.46
;zres@vpXF                  =  0.05
;zres@vpYF                  =  0.95


;--------------------------

zonalmean_1CO2=dim_avg_n_Wrap(TS_e_2CO2(:,:,:), (/2/))  

 
TStd_dev_1CO2     = dim_stddev_n_Wrap(zonalmean_1CO2(:,:), (/0/)) 
Xmean_change      = dim_avg_n_Wrap(Change_TS(1,:,:), (/1/) )    



 
R_STD_T = 2*(TStd_dev_1CO2)
L_STD_T = -1*(2*TStd_dev_1CO2)
copy_VarCoords(Xmean_change,R_STD_T)
copy_VarCoords(Xmean_change,L_STD_T)


print(R_STD_T)
print(L_STD_T)
 
nlat  = dimsizes(lat)  
SET1      = new ((/2,nlat/), float)
SET1(0,:)=R_STD_T(:)
SET1(1,:)=L_STD_T(:) 
 

;--------------------------


xStd_dev     = dim_stddev_n_Wrap(Change_TS(:,:,:), (/2/) )  
xmean_change = dim_avg_n_Wrap(Change_TS(:,:,:), (/2/) )  

R_STD = xmean_change+xStd_dev
L_STD = xmean_change-xStd_dev
copy_VarCoords(xmean_change,R_STD)
copy_VarCoords(xmean_change,L_STD)

   
SET      = new ((/12,nlat/), float)
SET(0,:)=R_STD(0,:)
SET(1,:)=L_STD(0,:)
SET(2,:)=R_STD(1,:)
SET(3,:)=L_STD(1,:)
SET(4,:)=R_STD(2,:)
SET(5,:)=L_STD(2,:)
SET(6,:)=R_STD(3,:)
SET(7,:)=L_STD(3,:)
SET(8,:)=R_STD(4,:)
SET(9,:)=L_STD(4,:)
SET(10,:)=R_STD(5,:)
SET(11,:)=L_STD(5,:)
 
 
print(R_STD&lat)
print(SET(0:1,:))

; Create a plot with the area between both curves filled in grey.
zres2 = zres
delete(zres2@xyLineColors)
zres2@gsnXYFillColors = "lightpink"
zres2@xyLineColor     = -1                           ; We don't want the line, so make it transparent.


zres3=zres2
zres3@gsnXYFillColors = "grey57"
zres3@xyLineColor     = -1                           ; We don't want the line, so make it transparent.3
;------------------------------------------------ 
Cases_Label = (/"(a) 1xCO~B1~2 ~NN~"," (b) Uniform","(c) Tropic","(d) Polar","(e) Arctic","(f) Antarctic"/)
               
  plot = new(6,graphic)
  cnres@gsnLeftString  = Cases_Label(0)
  cnres@gsnRightString = Label_Global_mean(0)
  plot(0)              = gsn_csm_contour_map(wks,Change_sig(0,:,:),cnres)
  zonal_mean           = gsn_csm_attach_zonal_means(wks,plot(0),Change_sig(0,:,:),zres)
  ;STD_plot  = gsn_csm_xy(wks,SET(0:1,:), R_STD&lat,zres2)  ; Create another filled XY plot.
  ;overlay(zonal_mean,STD_plot)
  STD_plot1  = gsn_csm_xy(wks,SET1(0:1,:), R_STD&lat,zres3)  ; Create another filled XY plot.
  overlay(zonal_mean,STD_plot1)
 

  cnres@gsnLeftString  = Cases_Label(1)
  cnres@gsnRightString = Label_Global_mean(1)
  plot(1)              = gsn_csm_contour_map(wks,Change_sig(1,:,:),cnres)
  zonal_mean           = gsn_csm_attach_zonal_means(wks,plot(1),Change_sig(1,:,:),zres)
  ;STD_plot  = gsn_csm_xy(wks,SET(2:3,:), R_STD&lat,zres2)  ; Create another filled XY plot.
  ;overlay(zonal_mean,STD_plot)
  STD_plot1  = gsn_csm_xy(wks,SET1(0:1,:), R_STD&lat,zres3)  ; Create another filled XY plot.
  overlay(zonal_mean,STD_plot1)

  cnres@gsnLeftString  = Cases_Label(2)
  cnres@gsnRightString = Label_Global_mean(2)  
  plot(2)              = gsn_csm_contour_map(wks,Change_sig(2,:,:),cnres)
  zonal_mean           = gsn_csm_attach_zonal_means(wks,plot(2),Change_sig(2,:,:),zres)
  ;STD_plot  = gsn_csm_xy(wks,SET(4:5,:), R_STD&lat,zres2)  ; Create another filled XY plot.
  ;overlay(zonal_mean,STD_plot)
  STD_plot1  = gsn_csm_xy(wks,SET1(0:1,:), R_STD&lat,zres3)  ; Create another filled XY plot.
  overlay(zonal_mean,STD_plot1)

  cnres@gsnLeftString  = Cases_Label(3) 
  cnres@gsnRightString = Label_Global_mean(3)
  plot(3)              = gsn_csm_contour_map(wks,Change_sig(3,:,:),cnres)
  zonal_mean           = gsn_csm_attach_zonal_means(wks,plot(3),Change_sig(3,:,:),zres)
  ;STD_plot  = gsn_csm_xy(wks,SET(6:7,:), R_STD&lat,zres2)  ; Create another filled XY plot.
  ;overlay(zonal_mean,STD_plot)
  STD_plot1  = gsn_csm_xy(wks,SET1(0:1,:), R_STD&lat,zres3)  ; Create another filled XY plot.
  overlay(zonal_mean,STD_plot1)

  cnres@gsnLeftString  = Cases_Label(4)
  cnres@gsnRightString = Label_Global_mean(4)
  plot(4)              = gsn_csm_contour_map(wks,Change_sig(4,:,:),cnres)
  zonal_mean          = gsn_csm_attach_zonal_means(wks,plot(4),Change_sig(4,:,:),zres)
  ;STD_plot  = gsn_csm_xy(wks,SET(8:9,:), R_STD&lat,zres2)  ; Create another filled XY plot.
  ;overlay(zonal_mean,STD_plot)
  STD_plot1  = gsn_csm_xy(wks,SET1(0:1,:), R_STD&lat,zres3)  ; Create another filled XY plot.
  overlay(zonal_mean,STD_plot1)

  cnres@gsnLeftString  = Cases_Label(5)
  cnres@gsnRightString = Label_Global_mean(5)
  plot(5)              = gsn_csm_contour_map(wks,Change_sig(5,:,:),cnres)
  zonal_mean          = gsn_csm_attach_zonal_means(wks,plot(5),Change_sig(5,:,:),zres)
  ;STD_plot  = gsn_csm_xy(wks,SET(10:11,:), R_STD&lat,zres2)  ; Create another filled XY plot.
  ;overlay(zonal_mean,STD_plot)  
  STD_plot1  = gsn_csm_xy(wks,SET1(0:1,:), R_STD&lat,zres3)  ; Create another filled XY plot.
  overlay(zonal_mean,STD_plot1)
;--------------------
; add markers to plot
;--------------------

  ;x = 1.0
  ;y = 2.0

  ;pmres               = True
  ;pmres@gsMarkerColor = "red"
  ;pmres@gsMarkerIndex = 16
  ;pmres@gsMarkerSizeF = 0.01
  ;dum = gsn_add_polymarker(wks,plot(0),x,y,pmres)

;------------------------------
; plot statistical significance
;------------------------------

  sgres                      = True		; significance
  sgres@gsnDraw              = False		; draw plot
  sgres@gsnFrame             = False		; advance frome
  sgres@cnInfoLabelOn        = False		; turn off info label
  sgres@cnLinesOn            = False		; draw contour lines
  sgres@cnLineLabelsOn       = False		; draw contour labels
  sgres@cnFillScaleF         = 0.5		; add extra density
  sgres@cnFillDotSizeF       = 0.002

  sgres@gsnAddCyclic         = True

; activate if gray shading for B&W plot  
  sgres@cnFillOn             = True
  sgres@cnFillColors         = (/"transparent","transparent"/) ; choose one color for our single cn level
  sgres@cnLevelSelectionMode = "ExplicitLevels"	         ; set explicit contour levels
  sgres@cnLevels             = 95.0                            ; only set one level
  sgres@lbLabelBarOn         = False

  sgres@tiMainString         = ""     ; title
  sgres@gsnCenterString      = ""  ; subtitle
  sgres@gsnLeftString        = ""    ; upper-left subtitle
  sgres@gsnRightString       = ""   ; upper-right subtitle

  sgres@cnConstFEnableFill   = True
  sgres@mpProjection          = "Robinson"


  ;sig_plot0 = gsn_csm_contour(wks,alpha(0,:,:),sgres)
  ;sig_plot1 = gsn_csm_contour(wks,alpha(1,:,:),sgres)
  ;sig_plot2 = gsn_csm_contour(wks,alpha(2,:,:),sgres)
  ;sig_plot3 = gsn_csm_contour(wks,alpha(3,:,:),sgres)
  ;sig_plot4 = gsn_csm_contour(wks,alpha(4,:,:),sgres)
  ;sig_plot5 = gsn_csm_contour(wks,alpha(5,:,:),sgres)

  ;opt                  = True
  ;opt@gsnShadeFillType = "pattern"
  ;opt@gsnShadeHigh     = 10


 
  
  opt                  = True
  opt@gsnShadeFillType = "pattern"
  ;opt@gsnShadeHigh     = 17
  opt@gsnShadeLow      = 17

  ;sig_plot0 = gsn_contour_shade(sig_plot0,95.0,-999,opt)
  ;sig_plot1 = gsn_contour_shade(sig_plot1,95.0,-999,opt)
  ;sig_plot2 = gsn_contour_shade(sig_plot2,95.0,-999,opt)
  ;sig_plot3 = gsn_contour_shade(sig_plot3,95.0,-999,opt)
  ;sig_plot4 = gsn_contour_shade(sig_plot4,95.0,-999,opt)
  ;sig_plot5 = gsn_contour_shade(sig_plot5,95.0,-999,opt)    

  ;overlay(plot(0),sig_plot0)
  ;overlay(plot(1),sig_plot1)
  ;overlay(plot(2),sig_plot2)
  ;overlay(plot(3),sig_plot3)
  ;overlay(plot(4),sig_plot4)
  ;overlay(plot(5),sig_plot5)
  
;************************************************
; create panel
;************************************************
  resP                           = True                ; modify the panel plot
  resP@gsnPanelMainString        = ""
  resP@gsnPanelLabelBar          = True                ; add common colorbar
  resP@lbLabelFontHeightF        = 0.007               ; make labels smaller
  resP@lbTitleOn                 = True
  resP@lbLabelStride             = 2
  resP@lbTitleString             = "K" ;"kJ/cm^2"
  resP@lbTitleFontHeightF        = 0.008
  resP@lbTitleFont               = "times-roman"
  resP@lbTitlePosition           = "Bottom"
  resP@pmLabelBarOrthogonalPosF  = -0.1
  resP@pmLabelBarWidthF            = 0.4                                         
  resP@pmLabelBarHeightF           = 0.05 
  ;resP@lbOrientation              = "Vertical"     ; vertical label bar
  resP@gsnPanelFigureStringsFont = "times-roman"
  resP@gsnPanelBottom            = 0.05                 ; add space at bottom
; resP@gsnMaximize               = True                 ; use full page
  resP@amJust   	          = "TopRight"
  resP@pmLabelBarOrthogonalPosF = -.03
  resP@pmLabelBarParallelPosF = .04
  resP@lbLabelFont               = "times-roman"
  resP@lbLabelFontHeightF  = 0.015     ; make labels smaller
  resP@lbTitleFontHeightF   =0.015
  resP@gsnPanelYWhiteSpacePercent = 2.5
  resP@gsnPanelXWhiteSpacePercent = 4.5
;  resP@txString   = "Temperature (~S~o~N~C)(model climatology - year 41 to 100)"
  resP@gsnMaximize      = True
  resP@gsnPaperOrientation = "portrait"


; resP@gsnPanelFigureStrings= (/Label_Global_mean_percent(0), Label_Global_mean_percent(1),Label_Global_mean_percent(2), Label_Global_mean_percent(3), Label_Global_mean_percent(4), Label_Global_mean_percent(5)/) ; add strings to panel
  resP@gsnPanelFigureFontHeightF = -0.1

Mark_sig95    = where(alpha.gt.95,alpha@missing_value, 0.)                                              
copy_VarCoords(alpha,Mark_sig95)

;----------------------------------------------------------------------
; Draw lines at the lon/lon coordinate array values.
;----------------------------------------------------------------------
  pres                   = True
  ;pres@gsnDraw           = False
  ;pres@gsnFrame          = False
  pres@gsnCoordsAsLines  = False
  pres@gsLineThicknessF  = 2
  pres@gsMarkerColor   = "darkorchid4"
  ;gsn_coordinates(wks,plot,diff_sig,pres)
  ;delete(pres@gsnCoordsAsLines)
;----------------------------------------------------------------------
; Draw the lat/lon grid with markers, using black for grid locations
; where the data is not missing, and red for grid locations
; where the data is missing.
;---------------------------------------------------------------------
  pres@gsnCoordsNonMissingColor = "black"
  pres@gsnCoordsMissingColor    = "transparent"
  pres@gsMarkerIndex            = 2  ;16
  pres@gsMarkerSizeF            = 5    ; Default is a little large
  pres@gsnCoordsAttach = True
  ;pres@mpLimitMode                 = "LatLon"
  ;pres@mpMinLatF                   = 0
  ;pres@mpMaxLatF                   = -90
  ;pres@mpMinLonF                   = 0
  ;pres@mpMaxLonF                   = 360

  gsn_coordinates(wks,plot(0),Mark_sig95(0,:,:),pres)
  gsn_coordinates(wks,plot(1),Mark_sig95(1,:,:),pres)
  gsn_coordinates(wks,plot(2),Mark_sig95(2,:,:),pres)
  gsn_coordinates(wks,plot(3),Mark_sig95(3,:,:),pres)
  gsn_coordinates(wks,plot(4),Mark_sig95(4,:,:),pres)
  gsn_coordinates(wks,plot(5),Mark_sig95(5,:,:),pres)

  gsn_panel(wks,plot,(/3,2/),resP)               ; now draw as one plot
  ;draw(plot)
  ;frame(wks)
  ;exit()

  delete(wks)

  cmd = "convert -geometry 2500x2500 -density 300 -trim " + pname + ".eps " + \
                                                          pname + ".png"
  system(cmd)
  
end
  
  
 