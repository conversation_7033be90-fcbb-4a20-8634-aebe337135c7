; Script to create spatial maps of temperature and precipitation differences
; between tropical case and 1CO2 case

load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_code.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/contributed.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/shea_util.ncl"

begin
  ; Define paths and load data files
  e_path="/home/<USER>/Documents/backup/my_work_draft/writeups/hydrological_cycle"
  e_1CO2 = addfile(e_path+"/Yearly_E_CO2_01_100new1.nc", "r")
  e_TROP = addfile("/home/<USER>/Documents/backup/my_work_draft/ITCZ/Yearly_E2000_2CO2_TROP_01_100_2D_1.nc", "r")
  
  ; Get latitude and longitude
  lat = e_1CO2->lat
  lon = e_1CO2->lon
  
  ; Calculate time averages for temperature (TS) and precipitation (PRECT)
  ; Using years 40-99 for analysis (steady state)
  TS_1CO2_time_avg = dim_avg_n_Wrap(e_1CO2->TS(40:99,:,:), 0)
  TS_TROP_time_avg = dim_avg_n_Wrap(e_TROP->TS(40:99,:,:), 0)
  
  ; Calculate differences (TROP - 1CO2)
  TS_diff = TS_TROP_time_avg - TS_1CO2_time_avg
  
  ; Copy coordinate information
  copy_VarCoords(TS_1CO2_time_avg, TS_diff)
  
  ; Calculate global mean temperature difference for reference
  rad = 4.0*atan(1.0)/180.0
  wgt = cos(lat * rad)
  ts_global_1co2 = wgt_areaave_Wrap(TS_1CO2_time_avg, wgt, 1.0, 0)
  ts_global_TROP = wgt_areaave_Wrap(TS_TROP_time_avg, wgt, 1.0, 0)
  ts_diff_global = ts_global_TROP - ts_global_1co2
  
  print("Global mean temperature difference (TROP - 1CO2): " + ts_diff_global + " K")
  jlat  = dimsizes( lat )
  rad    = 4.0*atan(1.0)/180.0
  re     = 6371220.0
  rr     = re*rad
  dlon   = abs(lon(2)-lon(1))*rr
  dx    = dlon*cos(lat*rad)
  dy     = new ( jlat, typeof(dx))
  dy(0)  = abs(lat(2)-lat(1))*rr
  dy(1:jlat-2)  = abs(lat(2:jlat-1)-lat(0:jlat-3))*rr*0.5   
  dy(jlat-1)    = abs(lat(jlat-1)-lat(jlat-2))*rr
  area   = dx*dy
  clat   = cos(lat*rad)
;****************************************************

 
Global_mean=wgt_areaave_Wrap( TS_diff,area,1.0,0)
copy_VarCoords( TS_diff,Global_mean)

Label_Global_mean=("Mean="+decimalPlaces(Global_mean,2,True)+"K")
print(Label_Global_mean)
  ; Create plots
  wks = gsn_open_wks("pdf", "TROP_vs_1CO2_spatial_maps")
  
  ; Common resources for both plots
  res = True
  res@gsnDraw = False
  res@gsnFrame = False
  res@gsnMaximize = True
  res@gsnAddCyclic = True
  
  res@mpFillOn = False
  res@mpOutlineOn = True
  res@mpCenterLonF = 180.0
  
  res@cnFillOn = True
  res@cnLinesOn = False
  res@cnLineLabelsOn = False
  res@cnInfoLabelOn = False
  res@lbLabelBarOn = True
  res@pmLabelBarOrthogonalPosF = -0.01
  
  ; Temperature plot resources
  resT = res
  resT@tiMainString = "Surface Temperature Difference (TROP - 1CO2)"
  resT@gsnLeftString = "TROP - 1CO2"
  resT@gsnRightString = "K"
  resT@tiMainFontHeightF = 0.015
resT@tiMainFontHeightF = 0.015
resT@tiMainFontHeightF = 0.015
  ; Use a diverging color map for temperature
  gsn_define_colormap(wks, "NCV_blu_red")
  resT@cnLevelSelectionMode = "ManualLevels"
  resT@cnMinLevelValF = -5.0
  resT@cnMaxLevelValF = 5.0
  resT@cnLevelSpacingF = 0.5
  resT@gsnPaperOrientation = "portrait"
  resT@tmYRBorderOn = False
  resT@tmYLBorderOn = False
  resT@tmXBBorderOn = False
  resT@tmXTBorderOn = False
  resT@tmYLOn               = False     ; Turn off top tickmarks
  resT@tmYROn               = False 
  resT@tmXBOn               = False     ; Turn off top tickmarks
  resT@tmXTOn               = False 
  resT@gsnRightString = Label_Global_mean
  resT@lbTitleString             = "K" ;"kJ/cm^2"
  resT@lbTitleFontHeightF        = 0.01
  resT@lbTitleFont               = "times-roman"
  resT@lbTitlePosition           = "Bottom"
  ; Create the plots
  plotT = gsn_csm_contour_map(wks, TS_diff, resT)
  
  ; Draw the plots
  draw(plotT)
  frame(wks)
  
  
  
end