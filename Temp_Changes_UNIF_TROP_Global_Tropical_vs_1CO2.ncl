; Script to plot surface temperature changes for UNIform case: global and tropical spatial plots (UNIF-1CO2)

load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_code.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/contributed.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/shea_util.ncl"

begin
  ; Define paths and load data files
  e_path="/home/<USER>/Documents/backup/my_work_draft/writeups/hydrological_cycle"
  e_1CO2 = addfile(e_path+"/Yearly_E_CO2_01_100new1.nc", "r")
  e_2CO2 = addfile(e_path+"/Yearly_E_2CO2_01_100new1.nc", "r")

  e_UNIF = addfile("/home/<USER>/Documents/backup/my_work_draft/ITCZ/Yearly_E_37_UNIF_01_100_new1.nc", "r")
  e_TROP =  addfile("/home/<USER>/Documents/backup/my_work_draft/ITCZ/Yearly_E2000_2CO2_TROP_01_100_2D_1.nc", "r")

  ; Get latitude and longitude
  lat = e_1CO2->lat
  lon = e_1CO2->lon
  
  ; Add units to lat and lon
  lat@units = "degrees_north"
  lon@units = "degrees_east"
  
  ; Calculate surface temperature differences with area weighting
  ; First average over time dimension (using years 40-99 for steady state)
  TS_1CO2_time_avg = dim_avg_n_Wrap(e_1CO2->TS(40:99,:,:), 0)
  TS_UNIF_time_avg = dim_avg_n_Wrap(e_UNIF->TS(40:99,:,:), 0)
  TS_TROP_time_avg = dim_avg_n_Wrap(e_TROP->TS(40:99,:,:), 0)
  ; Calculate difference with respect to 1CO2
  TS_diff_UNIF = TS_UNIF_time_avg - TS_1CO2_time_avg
  TS_diff_TROP = TS_TROP_time_avg - TS_1CO2_time_avg
  ; Copy coordinate information
  copy_VarCoords(TS_1CO2_time_avg, TS_diff_UNIF)
    copy_VarCoords(TS_1CO2_time_avg, TS_diff_TROP)

  ; Ensure coordinate units are set
  TS_diff_UNIF&lat@units = "degrees_north"
  TS_diff_UNIF&lon@units = "degrees_east"
  
    TS_diff_TROP&lat@units = "degrees_north"
  TS_diff_TROP&lon@units = "degrees_east"
  ; Calculate global and tropical mean temperature changes using wgt_areaave_Wrap
  rad = 4.0*atan(1.0)/180.0
  wgt = cos(lat * rad)
  
  ; Global mean (all latitudes)
  ts_diff_global_UNIF = wgt_areaave_Wrap(TS_diff_UNIF, wgt, 1.0, 0)
    ts_diff_global_TROP = wgt_areaave_Wrap(TS_diff_TROP, wgt, 1.0, 0)

  ; Tropical mean (30S-30N)
  lat_indices = ind(lat.ge.-30 .and. lat.le.30)
  ts_diff_tropical_UNIF = wgt_areaave_Wrap(TS_diff_UNIF(lat_indices,:), wgt(lat_indices), 1.0, 0)
    ts_diff_tropical_TROP = wgt_areaave_Wrap(TS_diff_TROP(lat_indices,:), wgt(lat_indices), 1.0, 0)

  Label_Global_mean_UNIF = "Global Mean="+decimalPlaces(ts_diff_global_UNIF,2,True)+ " K"
  Label_Tropical_mean_UNIF = "Tropical Mean="+decimalPlaces(ts_diff_tropical_UNIF,2,True)+ " K"
    Label_Tropical_mean_TROP = "Tropical Mean="+decimalPlaces(ts_diff_tropical_TROP,2,True)+ " K"

  print("Global mean temperature difference (UNIF - 2CO2): " + ts_diff_global_UNIF + " K")
  print("Tropical mean temperature difference (UNIF - 2CO2): " + ts_diff_tropical_UNIF + " K")
    print("Tropical mean temperature difference (TROP - 2CO2): " + ts_diff_tropical_TROP + " K")

  ; Create plots
  wks = gsn_open_wks("png", "Temp_Changes_UNIF_TROP_Global_Tropical_vs_1CO2")
    ;wks = gsn_open_wks("png", "Temp_Changes_UNIF_TROP_Tropical_vs_1CO2")

  ; Common resources for both plots
  res = True
  res@gsnDraw = False
  res@gsnFrame = False
  res@gsnMaximize = True
  res@gsnAddCyclic = True
  
  res@mpFillOn = False
  res@mpOutlineOn = True
  res@mpCenterLonF = 180.0
  
  ; Add grid lines
  res@mpGridAndLimbOn = True
  res@mpGridLatSpacingF = 30
  res@mpGridLonSpacingF = 60
  res@mpGridLineColor = "gray"
  res@mpGridLineThicknessF = 0.5
  res@mpGridLineDashPattern = 2
  
  ; Make sure longitude labels are displayed
  res@tmXBLabelsOn = True
  res@tmXBOn = True
  res@tmXTOn = False
  
  res@cnFillOn = True
  res@cnLinesOn = False
  res@cnLineLabelsOn = False
  res@cnInfoLabelOn = False
  res@lbLabelBarOn = False  ; Turn off individual label bars
  
  res@tmXBLabelFontHeightF    = 0.015    ; Make these labels smaller.
  res@tmYLLabelFontHeightF    = 0.015  ; Make these labels smaller.

  ; Use a diverging color map for temperature
  gsn_define_colormap(wks, "MPL_bwr")

  res@cnLevelSelectionMode = "ManualLevels"
  res@cnMinLevelValF = -1.0
  res@cnMaxLevelValF = 1.0
  res@cnLevelSpacingF = 0.25
  
  ; Create panel plot
  plot = new(3, graphic)
  
  ; Global UNIF - 1CO2 plot
  res@mpMaxLatF = 90
  res@mpMinLatF = -90
  res@gsnLeftString = "(a) UNIF - 1CO~B1~2 ~NN~"
  res@gsnRightString = Label_Global_mean_UNIF
  res@gsnStringFontHeightF =0.017
  plot(0) = gsn_csm_contour_map(wks, TS_diff_UNIF, res)
  
  ; Tropical UNIF - 1CO2 plot (30S-30N)
  res@mpMaxLatF = 40
  res@mpMinLatF = -40
  res@gsnLeftString = "(b) UNIF - 1CO~B1~2 ~NN~ (30~F34~0~F~S-30~F34~0~F~N)"
  res@gsnRightString = Label_Tropical_mean_UNIF
  res@gsnStringFontHeightF =0.017
  plot(1) = gsn_csm_contour_map(wks, TS_diff_UNIF, res)

  ; Tropical TROP - 1CO2 plot (30S-30N)
  res@mpMaxLatF = 40
  res@mpMinLatF = -40
  res@gsnLeftString = "(c) TROP - 1CO~B1~2 ~NN~ (30~F34~0~F~S-30~F34~0~F~N)"
  res@gsnRightString = Label_Tropical_mean_TROP
  res@gsnStringFontHeightF =0.017
  plot(2) = gsn_csm_contour_map(wks, TS_diff_TROP, res)
  
  ; Panel plot resources - fix colorbar visibility
  resP = True
  resP@gsnMaximize = True
  resP@gsnPanelLabelBar = True  ; Make sure this is set to True

  ; Adjust panel settings to add more space between plots and colorbar
  resP@pmLabelBarOrthogonalPosF = -0.05  ; Move colorbar closer to plots
  resP@gsnPanelYWhiteSpacePercent = 5.0  ; Add more vertical space between plots
  resP@gsnPanelXWhiteSpacePercent = 5.0  ; Add more horizontal space between plots

  ; Adjust colorbar settings to make it more visible
  resP@lbLabelFontHeightF = 0.014
  resP@lbTitleFontHeightF = 0.014
  resP@lbLabelStride = 2
  resP@lbBoxEndCapStyle = "TriangleBothEnds"
  resP@lbOrientation = "Horizontal"  ; Make colorbar horizontal
  resP@pmLabelBarWidthF = 0.7        ; Make colorbar wider
  resP@pmLabelBarHeightF = 0.09      ; Make colorbar taller
  resP@lbTitleString = "Temperature Change (K)"
  resP@lbTitleFont = "times-roman"
  resP@lbTitlePosition = "Bottom"
  resP@lbTitleFontHeightF = 0.014    ; Adjust title font size
  resP@lbLabelFontHeightF = 0.01     ; Adjust label font size

  ; Make sure the colorbar is drawn
  resP@lbLabelBarOn = True           ; Explicitly turn on the label bar

  ; Create the panel plot with explicit panel size
  gsn_panel(wks, plot(0:1), (/2,1/), resP)
  
end
