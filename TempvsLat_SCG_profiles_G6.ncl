load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_code.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/contributed.ncl"

begin
  ; === Load Files ===
  f_pr_geo  = addfile("/home/<USER>/Documents/Shinu_data/Analysis/pr_ensemble_G6sulfur_2020-2099.nc", "r")    ; [time,lat=180,lon=360]
  f_tas_geo = addfile("/home/<USER>/Documents/Shinu_data/Analysis/tas_ensemble_G6sulfur_2020-2099.nc", "r")

  rcp_precc = addfile("/home/<USER>/Documents/Shinu_data/RCP_merged/RCP8_5_PRECC_2010_2090.nc", "r")
  rcp_precl = addfile("/home/<USER>/Documents/Shinu_data/RCP_merged/RCP8_5_PRECL_2010_2090.nc", "r")       ; [time,lat=180,lon=360]
  f_tas_rcp = addfile("/home/<USER>/Documents/Shinu_data/RCP_merged/RCP8_5_TS_2010_2090.nc", "r")

  ; === Read Variables ===
  pr_geo  = f_pr_geo->pr_ensemble   ; mm/day
  tas_geo = f_tas_geo->tas_ensemble     ; K

  rcp_precc_data = rcp_precc->PRECC      ; [time,lat,lon]
  rcp_precl_data = rcp_precl->PRECL

  pr_rcp = (rcp_precc_data + rcp_precl_data) * 86400.0  ; convert to mm/day  tas_rcp = f_tas_rcp->tas_ensemble
tas_rcp = f_tas_rcp->TS

  ; === Area Weights ===
  lat = f_pr_geo->lat
  lon = f_pr_geo->lon
  wgt = cos(lat * 0.0174533)             ; radians
lat_rcp=f_tas_rcp->lat
lon_rcp=f_tas_rcp->lon

  ; === Time-Mean Fields ===
  pr_geo_avg  = dim_avg_n_Wrap(pr_geo, 1)     ; [lat]
  tas_geo_avg = dim_avg_n_Wrap(tas_geo, 1)

  pr_rcp_avg  = dim_avg_n_Wrap(pr_rcp, 0)
  tas_rcp_avg = dim_avg_n_Wrap(tas_rcp, 0)
  pr_rcp1=linint2_Wrap(lon_rcp, lat_rcp, pr_rcp_avg, True, lon, lat, 0)
ts_rcp1=linint2_Wrap(lon_rcp, lat_rcp, tas_rcp_avg, True, lon, lat, 0)
printVarSummary(ts_rcp1)
printVarSummary(pr_geo_avg)

pr_rcp_avg2=dim_avg_n_Wrap(pr_rcp1, 1)
tas_rcp_avg2=dim_avg_n_Wrap(ts_rcp1, 1)

 pr_ts_sulfur=pr_geo_avg/tas_geo_avg
 pr_ts_rcp=pr_rcp_avg2/tas_rcp_avg2
print(pr_ts_sulfur)
print(pr_ts_rcp)

wks = gsn_open_wks("pdf", "ppt_per_unit_warming_G6")

  res = True
  res@gsnDraw = True
  res@gsnFrame = True
  res@xyLineColors = (/"blue","red"/)
  res@xyLineLabels = (/"G6sulfur ","RCP8.5"/)
  res@tiMainString = "Precipitation per unit warming"
  res@tiYAxisString = "mm/day/K"
  res@tiXAxisString = "Latitude"
 printVarSummary(lat)
 printVarSummary(pr_ts_rcp)
 printVarSummary(pr_ts_sulfur)
  ; === Plot ===
  plot = gsn_csm_xy(wks, lat, (/pr_ts_sulfur, pr_ts_rcp/), res)

end
